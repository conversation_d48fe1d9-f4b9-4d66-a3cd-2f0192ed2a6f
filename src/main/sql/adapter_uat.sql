create database dataadapteruat;
use dataadapteruat;
grant all privileges on dataadapteruat.* to 'appsone'@'%';

-- a1_adapter_state
create table dataadapteruat.a1_adapter_state like dataadapter.a1_adapter_state;
insert into dataadapteruat.a1_adapter_state select * from dataadapter.a1_adapter_state;
select * from dataadapteruat.a1_adapter_state;

-- a1_loganalyzerkpis_service_mapper
create table dataadapteruat.a1_loganalyzerkpis_service_mapper like dataadapter.a1_loganalyzerkpis_service_mapper;
insert into dataadapteruat.a1_loganalyzerkpis_service_mapper select * from dataadapter.a1_loganalyzerkpis_service_mapper;

-- a1_logscan_endpt
create table dataadapteruat.a1_logscan_endpt like dataadapter.a1_logscan_endpt;
insert into dataadapteruat.a1_logscan_endpt select * from dataadapter.a1_logscan_endpt;

-- a1_logscan_heal_instance_mapper
create table dataadapteruat.a1_logscan_heal_instance_mapper like dataadapter.a1_logscan_heal_instance_mapper;
insert into dataadapteruat.a1_logscan_heal_instance_mapper select * from dataadapter.a1_logscan_heal_instance_mapper;

-- a1_logscan_kpis
create table dataadapteruat.a1_logscan_kpis like dataadapter.a1_logscan_kpis;
insert into dataadapteruat.a1_logscan_kpis select * from dataadapter.a1_logscan_kpis;

-- a1_logscan_service
create table dataadapteruat.a1_logscan_service like dataadapter.a1_logscan_service;
insert into dataadapteruat.a1_logscan_service select * from dataadapter.a1_logscan_service;

-- a1_logscan_filters
create table dataadapteruat.a1_logscan_filters like dataadapter.a1_logscan_filters;
insert into dataadapteruat.a1_logscan_filters select * from dataadapter.a1_logscan_filters;

-- a1_logscanquery_filter_mapping
create table dataadapteruat.a1_logscanquery_filter_mapping like dataadapter.a1_logscanquery_filter_mapping;
insert into dataadapteruat.a1_logscanquery_filter_mapping select * from dataadapter.a1_logscanquery_filter_mapping;
