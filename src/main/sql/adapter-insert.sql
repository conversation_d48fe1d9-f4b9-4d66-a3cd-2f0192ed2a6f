#KPI
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.extractors.http.DynatraceKpiExtractor', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.transformers.HealKPITransformer', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.loaders.HealKPIHttpLoader', 0);
#TRANS
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.extractors.http.DynatraceTransactionExtractor', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.transformers.HealCollatedTransactionTransformer', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader', 0);
#TOPOLOGY
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.extractors.http.DynatraceTopologyExtractor', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.transformers.HealTopologyTransformer', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.loaders.HealTopologyLoader', 0);
#EVENT
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.extractors.http.DynatraceEventExtractor', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.transformers.HealDomainEventTransformer', 0);
INSERT INTO appsone.adapter_chain_worker (`class_path`, `is_initialized`) VALUES ('com.heal.etladapter.loaders.HealEventDataLoader', 0);


INSERT INTO appsone.adapter_chain_config (`chain_identifier`, `sleep_interval`, `reload_interval_min`, `processor_thread_pool_size`, `loader_thread_pool_size`, `pull_historic_data`, `historic_start_date`, `historic_end_date`, `historic_interval`, `test_start_date`, `test_end_date`, `add_sysout_loader`, `disable_chain`, `back_pressure_strategy`, `back_pressure_max_size`, `delta_in_min`, `extractor_id`, `pre_processor`) VALUES ("Dynatrace_KPIs", 60, 1, 1, 1, 0, -1, -1, 0, -1, -1, 0, 0, "BUFFER", 1024, 1, (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'), "com.heal.etladapter.preprocessor.NullPreprocessor");
INSERT INTO appsone.adapter_chain_config (`chain_identifier`, `sleep_interval`, `reload_interval_min`, `processor_thread_pool_size`, `loader_thread_pool_size`, `pull_historic_data`, `historic_start_date`, `historic_end_date`, `historic_interval`, `test_start_date`, `test_end_date`, `add_sysout_loader`, `disable_chain`, `back_pressure_strategy`, `back_pressure_max_size`, `delta_in_min`, `extractor_id`, `pre_processor`) VALUES ("Dynatrace_Transactions", 60, 1, 1, 1, 0, -1, -1, 0, -1, -1, 0, 0, "BUFFER", 1024, 1, (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'), "com.heal.etladapter.preprocessor.NullPreprocessor");
INSERT INTO appsone.adapter_chain_config (`chain_identifier`, `sleep_interval`, `reload_interval_min`, `processor_thread_pool_size`, `loader_thread_pool_size`, `pull_historic_data`, `historic_start_date`, `historic_end_date`, `historic_interval`, `test_start_date`, `test_end_date`, `add_sysout_loader`, `disable_chain`, `back_pressure_strategy`, `back_pressure_max_size`, `delta_in_min`, `extractor_id`, `pre_processor`) VALUES ("Dynatrace_Topology", 60, 1, 1, 1, 0, -1, -1, 0, -1, -1, 0, 0, "BUFFER", 1024, 1, (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'), "com.heal.etladapter.preprocessor.NullPreprocessor");
INSERT INTO appsone.adapter_chain_config (`chain_identifier`, `sleep_interval`, `reload_interval_min`, `processor_thread_pool_size`, `loader_thread_pool_size`, `pull_historic_data`, `historic_start_date`, `historic_end_date`, `historic_interval`, `test_start_date`, `test_end_date`, `add_sysout_loader`, `disable_chain`, `back_pressure_strategy`, `back_pressure_max_size`, `delta_in_min`, `extractor_id`, `pre_processor`) VALUES ("Dynatrace_Events", 60, 1, 1, 1, 0, -1, -1, 0, -1, -1, 0, 0, "BUFFER", 1024, 1, (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'), "com.heal.etladapter.preprocessor.NullPreprocessor");

INSERT INTO appsone.adapter_extended_config (`csv_dir`, `adapter_chain_config_id`) VALUES ('/logs', (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_KPIs'));
INSERT INTO appsone.adapter_extended_config (`csv_dir`, `adapter_chain_config_id`) VALUES ('/logs', (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Transactions'));
INSERT INTO appsone.adapter_extended_config (`csv_dir`, `adapter_chain_config_id`) VALUES ('/logs', (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Topology'));
INSERT INTO appsone.adapter_extended_config (`csv_dir`, `adapter_chain_config_id`) VALUES ('/logs', (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Events'));

INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (1, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_KPIs'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealKPITransformer'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (2, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_KPIs'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (1, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Transactions'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealCollatedTransactionTransformer'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (2, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Transactions'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (1, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Topology'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (2, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Topology'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (1, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Events'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
INSERT INTO appsone.worker_chain_mapping (`order`, `adapter_chain_config_id`, `adapter_chain_worker_id`) VALUES (2, (select id from appsone.adapter_chain_config where chain_identifier='Dynatrace_Events'), (select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));

##KPI
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.kpi.url','https://gld34765.live.dynatrace.com/api/v2/metrics/query?pageSize=1000&metricSelector=%s&resolution=%sm&entitySelector=entityId(%s)&mzSelector=mzId(%s)',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('Authorization','dt0c01.XVFYVMA2HIHGYBSURIX4UZMI.OIK4ZXENF22UFIDMD4OZDXP2XKT7QIPLZCCQKKRTFJGW65OUMMGDX6YVYO7DPLPL',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('domain','dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("management.zone.id", "6066030016517584332",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('kpi.thread.pool.max.size','50',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('kpi.thread.pool.core.size','50',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('kpi.thread.pool.queue.capacity','5000',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('resolution','2',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('batch.size','20',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceKpiExtractor'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('domain','dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealKPITransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.instance','DEFAULT-INSTANCE',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealKPITransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.agent','DEFAULT-AGENT',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealKPITransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealKPITransformer'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.ssl.enabled','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.certificate','grpc-ca.crt',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.address','haproxy-node1.appnomic',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.port','9998',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.max.retry.attempts','-1',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.retry.interval','60',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealKPIGrpcLoader'));


## TXN
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.transaction.url','https://gld34765.live.dynatrace.com/api/v2/metrics/query?pageSize=1000&metricSelector=builtin:service.keyRequest.(response.time,count.total,errors.fourxx.count)&resolution=1m&mzSelector=mzId(%s)',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('Authorization','dt0c01.XVFYVMA2HIHGYBSURIX4UZMI.OIK4ZXENF22UFIDMD4OZDXP2XKT7QIPLZCCQKKRTFJGW65OUMMGDX6YVYO7DPLPL',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("transaction.thread.pool.max.size", "50",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("transaction.thread.pool.core.size", "50",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("transaction.thread.pool.queue.capacity", "5000",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("management.zone.id", "6066030016517584332",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.entity.url','https://gld34765.live.dynatrace.com/api/v2/entities/%s?pageSize=1000',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('domain','dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealCollatedTransactionTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('account.name','Dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealCollatedTransactionTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.agent','defaultAgent',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealCollatedTransactionTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('url.normalize','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealCollatedTransactionTransformer'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.ssl.enabled','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.certificate','grpc-ca.crt',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.address','haproxy-node1.appnomic',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.port','9998',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.max.retry.attempts','-1',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.retry.interval','60',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealCollatedTransactionGrpcLoader'));


##TOPOLOGY
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.service.url','https://gld34765.live.dynatrace.com/api/v2/entities?pageSize=1000&entitySelector=mzId(%s),type(SERVICE)&includeDetails=true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('Authorization','dt0c01.XVFYVMA2HIHGYBSURIX4UZMI.OIK4ZXENF22UFIDMD4OZDXP2XKT7QIPLZCCQKKRTFJGW65OUMMGDX6YVYO7DPLPL',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.entity.url','https://gld34765.live.dynatrace.com/api/v2/entities/%s?pageSize=1000',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("management.zone.id", "6066030016517584332",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("domain", "dynatrace",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTopologyExtractor'));

INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('account.name', 'ubi',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('reload-configuration', 'true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('domain', 'dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('topology.application.identifier', 'dt-application',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('dt.component.tag.mapping.name', 'DtComponentMapper',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('dt.service.layer.tag.mapping.name', 'DtServiceLayer',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('topology.time.zone', '(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('environment.name', 'NONE',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('remove.instances', 'false',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.address','haproxy-node1.appnomic',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('grpc.port','9998',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealTopologyTransformer'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('controlcenter.instance.delete.url','https://haproxy-node1.appnomic:9998/heal-controlcenter/v2.0/api/accounts/ubi/instances?instanceRemoval=true&instanceIdentifier=',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('controlcenter.instance.add.url','https://haproxy-node1.appnomic:9998/heal-controlcenter/v2.0/api/accounts/ubi/instances',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('controlcenter.service.add.url','https://haproxy-node1.appnomic:9998/heal-controlcenter/v2.0/api/accounts/ubi/services',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('controlcenter.agent.add.url','https://haproxy-node1.appnomic:9998/heal-controlcenter/v2.0/api/accounts/ubi/agents-config',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('controlcenter.service.connection.url','https://haproxy-node1.appnomic:9998/heal-controlcenter/v2.0/api/accounts/%s/connections',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('batch.size','20',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));
INSERT INTO appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`) VALUES ('domain', 'dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealTopologyLoader'));


##EVENT
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.event.url','https://gld34765.live.dynatrace.com/api/v2/events?pageSize=1000&eventSelector=managementZoneId(%s),status(OPEN)',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('Authorization','dt0c01.XVFYVMA2HIHGYBSURIX4UZMI.OIK4ZXENF22UFIDMD4OZDXP2XKT7QIPLZCCQKKRTFJGW65OUMMGDX6YVYO7DPLPL',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.kpi.identifier','DYNATRACE',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.kpi.id','1234',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("management.zone.id", "6066030016517584332",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("dt.events.txn.regex", "The current (?P<metricname>.+?)\\s*\\(\\D*(?P<actualValue>\\d+(?:\\.\\d+)?)\\D*\\).*?threshold\\s*\\(\\D*(?P<thresholdValue>\\d+(?:\\.\\d+)?)\\D*\\)",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("metric.name", "metricname",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("metric.attribute", "metricAttribute",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("actual.value", "actualValue",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("threshold.value", "thresholdValue",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("event.metric.collection", "false",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("event.thread.pool.max.size", "50",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("event.thread.pool.core.size", "50",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("event.thread.pool.queue.capacity", "5000",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.agent','DEFAULT-AGENT',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.service','DEFAULT-SERVICE',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.event.nextpageurl','https://gld34765.live.dynatrace.com/api/v2/events?nextPageKey=%s',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.kpi.url','https://gld34765.live.dynatrace.com/api/v2/metrics/query?pageSize=1000&metricSelector=%s&resolution=%sm&entitySelector=entityId(%s)&mzSelector=mzId(%s)',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("dt.events.kpi.regex", "(?<metricname>\\bbuiltin.*?)(?=\\):filter|:filter).*?filter\\s*\\(\\s*(?P<metricAttribute>[^()]*?(?:\\([^()]*?\\)[^()]*?)*?)\\s*\\)",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("resolution", "1",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values("domain", "dynatrace",(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceEventExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.entity.url','https://gld34765.live.dynatrace.com/api/v2/entities/%s?pageSize=1000',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('dt.event.tag.mapping.name','https://gld34765.live.dynatrace.com/api/v2/entities/%s?pageSize=1000',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.extractors.http.DynatraceTransactionExtractor'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('domain','dynatrace',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('kpi.mapping.by.identifier','false',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('data.regex','Condition<br><b>(?<metricname>.*?)<\\/b>.*?<b>(?<actualValue>.*?)<\\/b>.*?<b>(?<operationType>.*?)<\\/b>.*?<b>(?<threshold>.*?)<\\/b>',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('value','actualValue',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('threshold','threshold',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('operation.type','operationType',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('account.name','ubi',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.instance','DEFAULT-INSTANCE',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.service','DEFAULT-SERVICE',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('default.app','DEFAULT-APP',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.transformers.HealDomainEventTransformer'));

insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.enabled','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.server.host','**************',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.server.port','5671',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.routing.key','EventData',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.ssl.enabled','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.thread.pool.size','50',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('os.enabled','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('os.thread.pool.size','50',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('event.data.timezone','Asia/Kolkata',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('account.name','ubi',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.nodes','192.168.13.109:9200,192.168.13.109:9225,192.168.13.109:9250',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.protocol','https',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.username','healadmin',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.password','cm9vdEAxMjM=',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.max.connections.per.route','10',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.max.connections.total','20',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.connection.timeout.sec','5',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.connection.socket.timeout.secs','10',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.batch.size','500',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('opensearch.batch.queue.max.size','5000',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('reload-configuration','true',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('anomaly-mq-exchange','anomaly-mq',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('anomaly-mq-routing-key','anomaly-mq-key',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('anomaly-signal-exchange','anomaly-signal',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('anomaly-signal-routing-key','anomaly-signal-key',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.mle.exchange','mq-mle',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));
insert into appsone.worker_parameters (`name`, `value`, `adapter_chain_worker_id`)  values('mq.mle.routing.key','mq-mle-key',(select id from appsone.adapter_chain_worker where class_path='com.heal.etladapter.loaders.HealEventDataLoader'));


insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.cpu.usage",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.disk.free",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.mem.usage",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.cpu.usage",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.disk.free",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.mem.usage",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.packets.tx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.packets.rx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.packets.errors",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.packets.dropped",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.linkUtilRx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.linkUtilTx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.packets.tx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.packets.rx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.packets.errors",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.packets.dropped",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.linkUtilRx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.linkUtilTx",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.uptime",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.uptime",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-A252AA0F61F21ED4","builtin:host.net.nic.traffic",'dynatrace');
insert into appsone.appsone.domain_entity_metric_mapping (`entity_identifier`, `metric_identifier`,`domain`) values("HOST-EA64C4E16DCFDC51","builtin:host.net.nic.traffic",'dynatrace');


INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_CPU_USAGE_Percentage','builtin:host.cpu.usage',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_DISK_AVAILABLE_Percentage','builtin:host.disk.free',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_MEMORY_USED_Percentage','builtin:host.mem.usage',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_PACKETS_SENT','builtin:host.net.nic.packets.tx',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_PACKETS_RECEIVED','builtin:host.net.nic.packets.rx',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_PACKETS_ERRORS','builtin:host.net.nic.packets.errors',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_PACKETS_DROPPED','builtin:host.net.nic.packets.dropped',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_RECEIVED_LINK_UTILIZATION','builtin:host.net.nic.linkUtilRx',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_SENT_LINK_UTILIZATION','builtin:host.net.nic.linkUtilTx',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('HEAL_HOST_AVAILABILITY','builtin:host.availability.state',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_UPTIME','builtin:host.uptime',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_NET_TRAFFIC','builtin:host.net.nic.traffic',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('JVM_THREAD_COUNT','builtin:tech.jvm.threads.count',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('response time','response time',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_memory_gc_activationCount','builtin:tech.jvm.memory.gc.activationCount',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_memory_gc_collectionTime','builtin:tech.jvm.memory.gc.collectionTime',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_memory_pool_used','builtin:tech.jvm.memory.pool.used',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_memory_runtime_free','builtin:tech.jvm.memory.runtime.free',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_memory_runtime_total','builtin:tech.jvm.memory.runtime.total',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_threads_totalCpuTime','builtin:tech.jvm.threads.totalCpuTime',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_memory_gc_suspensionTime','builtin:tech.jvm.memory.gc.suspensionTime',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_generic_processCount','builtin:tech.generic.processCount',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_generic_cpu_usage','builtin:tech.generic.cpu.usage',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_generic_mem_usage','builtin:tech.generic.mem.usage',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ('DT_JVM_APP_jvm_generic_count','builtin:tech.generic.count',NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("CPU I/O Wait","builtin:host.cpu.iowait",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("CPU Steal","builtin:host.cpu.steal",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("CPU Other","builtin:host.cpu.other",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("CPU Idle","builtin:tech.oracleDb.cd.cpu.idle",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("CPU System Time","builtin:host.cpu.system",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("System Load","builtin:host.cpu.load",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("CPU User Time","builtin:host.cpu.user",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Available Bytes","builtin:host.disk.avail",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Average Queue Length","builtin:host.disk.queueLength",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Read Bytes Per Second","builtin:host.disk.bytesRead",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Read Operations Per Second","builtin:host.disk.readOps",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Throughput Read","builtin:host.disk.throughput.read",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Throughput Write","builtin:host.disk.throughput.write",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Used %","builtin:host.disk.usedPct",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Used Bytes","builtin:host.disk.used",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Utilization Time","builtin:host.disk.utilTime",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Write Bytes Per Second","builtin:host.disk.bytesWritten",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Write Operations Per Second","builtin:host.disk.writeOps",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Disk Write Time","builtin:host.disk.writeTime",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Memory Available","builtin:host.mem.avail.bytes",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Memory Available %","builtin:host.mem.avail.pct",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Memory Reclaimable","builtin:host.mem.recl",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Memory Total","builtin:host.mem.total",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Memory Used","builtin:host.mem.used",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Swap Memory Total","builtin:host.mem.swap.total",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Swap Memory Used","builtin:host.mem.swap.used",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("File Descriptors Used","builtin:host.handles.fileDescriptorsUsed",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("File Descriptors Total","builtin:host.handles.fileDescriptorsMax",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Bytes Received","builtin:tech.generic.network.bytesRx",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Bytes Sent","builtin:tech.generic.network.bytesTx",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Received Packets Errors","builtin:host.net.nic.packets.errorsRx",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Received Packets Dropped","builtin:host.net.nic.packets.droppedRx",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Sent Packets Dropped","builtin:host.net.nic.packets.droppedTx",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Packets Received","builtin:tech.nettracer.pkts_rx",NULL,0,NULL,'dynatrace');
INSERT INTO appsone.domain_metric (`name`,`identifier`,`aggregation_level`,`is_group`,`group_name`,`domain`) VALUES ("Packets Sent","builtin:tech.generic.network.packets.tx",NULL,0,NULL,'dynatrace');


INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_CPU_USAGE_Percentage','builtin:host.cpu.usage');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_DISK_AVAILABLE_Percentage','builtin:host.disk.free');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_MEMORY_USED_Percentage','builtin:host.mem.usage');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_PACKETS_SENT','builtin:host.net.nic.packets.tx');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_PACKETS_RECEIVED','builtin:host.net.nic.packets.rx');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_PACKETS_ERRORS','builtin:host.net.nic.packets.errors');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_PACKETS_DROPPED','builtin:host.net.nic.packets.dropped');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_RECEIVED_LINK_UTILIZATION','builtin:host.net.nic.linkUtilRx');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_SENT_LINK_UTILIZATION','builtin:host.net.nic.linkUtilTx');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','HEAL_HOST_AVAILABILITY','builtin:host.availability.state');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_UPTIME','builtin:host.uptime');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_NET_TRAFFIC','builtin:host.net.nic.traffic');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','JVM_THREAD_COUNT','builtin:tech.jvm.threads.count');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','response time','response time');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_memory_gc_activationCount','builtin:tech.jvm.memory.gc.activationCount');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_memory_gc_collectionTime','builtin:tech.jvm.memory.gc.collectionTime');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_memory_pool_used','builtin:tech.jvm.memory.pool.used');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_memory_runtime_free','builtin:tech.jvm.memory.runtime.free');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_memory_runtime_total','builtin:tech.jvm.memory.runtime.total');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_threads_totalCpuTime','builtin:tech.jvm.threads.totalCpuTime');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_memory_gc_suspensionTime','builtin:tech.jvm.memory.gc.suspensionTime');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_generic_processCount','builtin:tech.generic.processCount');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_generic_cpu_usage','builtin:tech.generic.cpu.usage');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_generic_mem_usage','builtin:tech.generic.mem.usage');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace','DT_JVM_APP_jvm_generic_count','builtin:tech.generic.count');
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"CPU I/O Wait","builtin:host.cpu.iowait");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"CPU Steal","builtin:host.cpu.steal");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"CPU Other","builtin:host.cpu.other");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"CPU Idle","builtin:tech.oracleDb.cd.cpu.idle");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"CPU System Time","builtin:host.cpu.system");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"System Load","builtin:host.cpu.load");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"CPU User Time","builtin:host.cpu.user");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Available Bytes","builtin:host.disk.avail");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Average Queue Length","builtin:host.disk.queueLength");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Read Bytes Per Second","builtin:host.disk.bytesRead");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Read Operations Per Second","builtin:host.disk.readOps");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Throughput Read","builtin:host.disk.throughput.read");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Throughput Write","builtin:host.disk.throughput.write");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Used %","builtin:host.disk.usedPct");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Used Bytes","builtin:host.disk.used");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Utilization Time","builtin:host.disk.utilTime");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Write Bytes Per Second","builtin:host.disk.bytesWritten");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Write Operations Per Second","builtin:host.disk.writeOps");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Disk Write Time","builtin:host.disk.writeTime");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Memory Available","builtin:host.mem.avail.bytes");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Memory Available %","builtin:host.mem.avail.pct");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Memory Reclaimable","builtin:host.mem.recl");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Memory Total","builtin:host.mem.total");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Memory Used","builtin:host.mem.used");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Swap Memory Total","builtin:host.mem.swap.total");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Swap Memory Used","builtin:host.mem.swap.used");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"File Descriptors Used","builtin:host.handles.fileDescriptorsUsed");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"File Descriptors Total","builtin:host.handles.fileDescriptorsMax");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Bytes Received","builtin:tech.generic.network.bytesRx");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Bytes Sent","builtin:tech.generic.network.bytesTx");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Received Packets Errors","builtin:host.net.nic.packets.errorsRx");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Received Packets Dropped","builtin:host.net.nic.packets.droppedRx");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Sent Packets Dropped","builtin:host.net.nic.packets.droppedTx");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Packets Received","builtin:tech.nettracer.pkts_rx");
INSERT INTO dynatrace_connector.domain_to_heal_kpi_mappings (`domain`,`heal_kpi_identifier`,`src_kpi_identifier`) VALUES ('dynatrace',"Packets Sent","builtin:tech.generic.network.packets.tx");
