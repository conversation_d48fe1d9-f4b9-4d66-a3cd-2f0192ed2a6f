SET SQL_SAFE_UPDATES = 0;
use dataadapter;

drop table if exists `xpt_filters`;
create table `xpt_filters`
(
    id int not null auto_increment primary key,
    `filter_field` varchar(256) not null,
    `filter_values` varchar(1024) not null,
    `filter_type` varchar(1) not null default '+',
    `separator` varchar(1) not null default ','
)ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;

drop table if exists `xpt_journey_master`;
create table `xpt_journey_master`
(
     id int not null auto_increment primary key,
    `journey_name` varchar(256) not null,
    `txn_fqtn` varchar(256) not null,
    `txn_type` varchar(32) not null
)ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;

drop table if exists `xpt_kpi_default_logscan_instance_mapper`;
create table `xpt_kpi_default_logscan_instance_mapper`
(
     id int not null auto_increment primary key,
     `kpi_id` int not null,
    `logscan_instance` varchar(256) not null
)ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;

drop table if exists `xpt_metric_filter_map`;
create table `xpt_metric_filter_map`
(
    `id` int not null auto_increment primary key,
    `filter_id` int not null,
    `metric_id` int not null
)ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;

drop table if exists `xpt_metrics`;
create table `xpt_metrics`
(
     id int not null auto_increment primary key,
    `search_name` varchar(256) not null,
    `journey` varchar(256) not null,
    `aggregate_field` varchar(256) not null,
    `search_field` varchar(256) not null,
    `txn_category` varchar(256) not null
)ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;

drop table if exists `xpt_kpi_master`;
create table `xpt_kpi_master`
(
     id int not null auto_increment primary key,
    `kpi_name` varchar(256) not null,
    `kpi_id` int not null,
    `is_group_kpi` int  not null,
    `kpi_type` varchar(256) not null,
    `group_name` varchar(256) not null,
    `expression` varchar(256) not null,
    `service_name` varchar(256) not null,
    `entry_metric_label` varchar(256) not null,
    `exit_metric_label` varchar(256) not null
)ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;


