-- Queries
-- delete from dataadapter.a1_logscan_kpis;
-- delete from dataadapter.a1_loganalyzerkpis_service_mapper;
-- delete from dataadapter.a1_logscan_service;

select * from dataadapter.a1_logscan_kpis;
select * from dataadapter.a1_logscan_service;
select * from dataadapter.a1_logscan_heal_instance_mapper;
select * from dataadapter.a1_loganalyzerkpis_service_mapper;
select * from dataadapter.a1_logscan_endpt;

select * from dataadapter.a1_loganalyzerkpis_service_mapper where svc_id = 6880;

UPDATE `dataadapter`.`a1_loganalyzerkpis_service_mapper` SET `kpi_id` = '6879' WHERE (`svc_id` = '6881') and (`kpi_id` = '6881');
UPDATE `dataadapter`.`a1_loganalyzerkpis_service_mapper` SET `kpi_id` = '6881' WHERE (`svc_id` = '6880') and (`kpi_id` = '6879');

select * from dataadapter.a1_logscan_heal_instance_mapper;
update dataadapter.a1_logscan_kpis set `logscan_endpt` = (select distinct id from `a1_logscan_endpt` limit 1);

UPDATE `dataadapter`.`a1_logscan_kpis` SET `enabled` = '0' WHERE (`kpi_id` = '6880');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `enabled` = '0' WHERE (`kpi_id` = '6881');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `enabled` = '0' WHERE (`kpi_id` = '6882');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `heal_kpi_uid` = '47', `enabled` = '1' WHERE (`kpi_id` = '6883');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `enabled` = '0' WHERE (`kpi_id` = '6884');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `enabled` = '0' WHERE (`kpi_id` = '6885');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `enabled` = '0' WHERE (`kpi_id` = '6886');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `heal_kpi_name` = 'Established', `heal_kpi_uid` = '43' WHERE (`kpi_id` = '6878');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `heal_kpi_name` = 'Close Wait', `heal_kpi_uid` = '44' WHERE (`kpi_id` = '6879');
UPDATE `dataadapter`.`a1_logscan_kpis` SET `heal_kpi_name` = 'Fin Wait' WHERE (`kpi_id` = '6883');

UPDATE `dataadapter`.`a1_logscan_heal_instance_mapper` SET `heal_agent_uid` = '3e8edf6-3b17-4cf3-a967-d1cac81ec22b-2020_03_May' WHERE (`logscan_instance_name` = 'inst1');
UPDATE `dataadapter`.`a1_logscan_heal_instance_mapper` SET `heal_agent_uid` = '3e8edf6-3b17-4cf3-a967-d1cac81ec22b-2020_03_May' WHERE (`logscan_instance_name` = 'inst2');
UPDATE `dataadapter`.`a1_logscan_heal_instance_mapper` SET `heal_agent_uid` = '3e8edf6-3b17-4cf3-a967-d1cac81ec22b-2020_03_May' WHERE (`logscan_instance_name` = 'inst3');

select * from dataadapter.a1_adapter_state;
select * from dataadapter.a1_logscan_endpt;
select * from dataadapter.a1_logscan_kpis;

insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-apsdg-was-syserr-*', 'apsdg', 'was-syserr');
insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-apsdg-was-sysout-*', 'apsdg', 'was-sysout');

insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('java.lang.NoClassDefFoundError', 'APS_LA_SysErr_NoClassDefFoundError', 475,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('javax.net.ssl.SSLHandshakeException', 'APS_LA_SysErr_SSLHandshakeException', 476,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('java.lang.OutOfMemoryError', 'APS_LA_SysErr_OOM', 477,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('java.net.UnknownHostException', 'APS_LA_SysErr_DNS_Resolve_Error', 478,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('java.net.SocketTimeoutException', 'APS_LA_SysErr_Web_Time_Out', 479,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('java.lang.SecurityException', 'APS_LA_SysErr_SecurityException', 480,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('\"SRVE0100E\"=True', 'APS_LA_SysOut_Servlet_Init_Failed', 482,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('\"createOrWaitForConnection for resource\"=True', 'APS_LA_SysOut_Con_Wait_Time_Out', 483,  '', 0, 'payload', 6878, 1, 'Core');
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_group_name, is_group_kpi, logscan_field_name, logscan_endpt, enabled, heal_kpi_type)
    values ('\"WSVR0605W\"=True', 'APS_LA_SysOut_HungThread', 481,  '', 0, 'payload', 6878, 1, 'Core');

select count(*) from dataadapter.a1_logscan_kpis where heal_kpi_name like 'APS_LA%';
select * from dataadapter.a1_logscan_kpis where heal_kpi_name like 'APS_LA%';

update dataadapter.a1_logscan_kpis set heal_kpi_name = 'APS_LA_SysOut_Servlet_Init_Failed', heal_kpi_uid=482, kpi_pattern='\"SRVE0100E\"=True' where heal_kpi_name='APS_LA_SysOut_Servlet_Init_Failed';
update dataadapter.a1_logscan_kpis set heal_kpi_name = 'APS_LA_SysOut_Con_Wait_Time_Out', heal_kpi_uid=483, kpi_pattern='\"createOrWaitForConnection for resource\"=True' where heal_kpi_name='APS_LA_SysOut_Con_Wait_Time_Out';

insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-syserr'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysErr_NoClassDefFoundError'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-syserr'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysErr_SSLHandshakeException'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-syserr'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysErr_OOM'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-syserr'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysErr_DNS_Resolve_Error'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-syserr'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysErr_Web_Time_Out'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-syserr'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysErr_SecurityException'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-sysout'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysOut_Servlet_Init_Failed'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-sysout'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysOut_Con_Wait_Time_Out'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
    (select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdg' and logfile_type='was-sysout'),
    (select kpi_id from dataadapter.a1_logscan_kpis where heal_kpi_name = 'APS_LA_SysOut_HungThread'));

insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-apsdg-was-syserr-*', 'apsdg', 'was-syserr');

describe dataadapter.a1_logscan_kpis;
alter table dataadapter.a1_logscan_kpis drop index kpi_pattern;

select * from dataadapter.a1_logscan_endpt;
UPDATE `dataadapter`.`a1_logscan_endpt` SET `host` = 'master-of-hotness' WHERE (`id` = '6878');
UPDATE `dataadapter`.`a1_logscan_endpt` SET `host` = '*************' WHERE (`id` = '6878');
