-- Seed

-- a1_logscan_heal_instance_mapper
INSERT INTO `dataadapter`.`a1_logscan_heal_instance_mapper` (`logscan_instance_name`, `heal_instance_name`, `heal_agent_uid`) VALUES ('inst1', 'heal_app_instance_1', '4000');
INSERT INTO `dataadapter`.`a1_logscan_heal_instance_mapper` (`logscan_instance_name`, `heal_instance_name`, `heal_agent_uid`) VALUES ('inst2', 'heal_app_instance_2', '5000');
INSERT INTO `dataadapter`.`a1_logscan_heal_instance_mapper` (`logscan_instance_name`, `heal_instance_name`, `heal_agent_uid`) VALUES ('inst3', 'heal_db_instance_2', '6003');
INSERT INTO `dataadapter`.`a1_logscan_heal_instance_mapper` (`logscan_instance_name`, `heal_instance_name`, `heal_agent_uid`) VALUES ('inst4', 'heal_web_instance_15', '2342');


insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('javax.xml.ws.WebServiceException', 'WEB_SERVICE_EXCEPTION', 3876, 'Core', '', 0);
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('javax.net.ssl.SSLHandshakeException', 'SSL_HANDSHAKE_EXCEPTION', 3873, 'Core', '', 0);
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('java.net.SocketTimeoutException', 'SOCKET_TIMEOUT', 3874, 'Core', '', 0);
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('Connection reset by peer', 'CONNECTION_RESET', 3875, 'Core', '', 0);
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('java.lang.SecurityException', 'SECURITY_EXCEPTION', 3879, 'Core', '', 0);
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('Error Code :: U69', 'UPI_ERR_U69', 3878, 'Core', 'UPI', 1);
insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi) 
	values ('Error Code :: U30', 'UPI_ERR_U30', 3877, 'Core', 'UPI', 1);

insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-upi-scl-*', 'upi', 'scl');
insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-netbanking-was-syserr-*', 'netbanking', 'was-syserr');
insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-hdfc-pg-orcl-gg-*', 'hdfc-pg', 'orcl-gg');
insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-hdfc-pg-apptxn-*', 'hdfc-pg', 'apptxn');
insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-obp-wldiag-ofss-*', 'obp', 'wldiag-ofss');
insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-apsdigital-was-syserr-*', 'apsdigital', 'SystemErr');

-- upi
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'upi'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'Error Code :: U69'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'upi'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'Error Code :: U30'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'upi'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'java.net.SocketTimeoutException'));
        
-- obp
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'obp'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'javax.xml.ws.WebServiceException'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'obp'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'java.net.SocketTimeoutException'));
-- pg
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'hdfc-pg' and logfile_type='apptxn'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'javax.net.ssl.SSLHandshakeException'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'hdfc-pg' and logfile_type='orcl-gg'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'Connection reset by peer'));
        
-- netbanking
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'netbanking'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'java.net.SocketTimeoutException'));
        
-- apsdigial

insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'apsdigital'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'java.lang.SecurityException'));

insert into dataadapter.a1_logscan_service (index_pattern, service_name, logfile_type) values ('logscan-raw-netbanking-ihs-acl*', 'netbanking', 'ihs-acl');

insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi, logscan_field_name) 
	values ('rsa.js', 'RSA_JS', 3876, 'Core', '', 0, 'message');
	insert into dataadapter.a1_logscan_kpis (kpi_pattern,heal_kpi_name, heal_kpi_uid, heal_kpi_type,heal_group_name, is_group_kpi, logscan_field_name) 
	values ('des.js', 'DES_JS', 3874, 'Core', '', 0, 'message');

insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'netbanking' and logfile_type='ihs-acl'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'rsa.js'));
insert into dataadapter.a1_loganalyzerkpis_service_mapper (svc_id, kpi_id) values (
		(select svc_id from dataadapter.a1_logscan_service where service_name = 'netbanking' and logfile_type='ihs-acl'),
        (select kpi_id from dataadapter.a1_logscan_kpis where kpi_pattern = 'des.js'));

INSERT INTO `dataadapter`.`a1_logscan_endpt` (`host`, `port`, `protocol`) VALUES ('127.0.0.1', '9200', 'http');

INSERT INTO `dataadapter`.`a1_logscan_heal_instance_mapper` (`logscan_instance_name`, `heal_instance_name`, `heal_agent_uid`) VALUES ('nb_web_24', 'heal_web_instance_15', '2342');

