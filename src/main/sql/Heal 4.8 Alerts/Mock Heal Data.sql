SET SQL_SAFE_UPDATES = 0;
create user if not exists 'appsone'@'%' IDENTIFIED with mysql_native_password BY 'password';
alter user 'appsone'@'%' IDENTIFIED with mysql_native_password BY 'password';
create database dataadapter;
use dataadapter;
grant all privileges on dataadapter.* to 'appsone'@'%';

CREATE TABLE `COMPONENTSTATICTHRESHOLDDEF` (
  `ComponentInstanceId` int(11) NOT NULL,
  `ComponentId` int(11) NOT NULL,
  `KPIId` int(11) NOT NULL,
  `Severity` int(1) NOT NULL DEFAULT '1',
  `KPIName` varchar(100) NOT NULL,
  `Operation` varchar(25) NOT NULL DEFAULT 'greater than',
  `Min` double NOT NULL DEFAULT '0',
  `Max` double NOT NULL DEFAULT '0',
  PRIMARY KEY (`ComponentInstanceId`,`KPIId`,`ComponentId`,`Severity`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1; 

CREATE TABLE `ALERTS` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `AlertProfileId` int(11) NOT NULL,
  `Name` varchar(128) NOT NULL,
  `Value` double NOT NULL,
  `CurrentTime` datetime NOT NULL,
  `Severity` tinyint(1) NOT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=6878 DEFAULT CHARSET=latin1;

CREATE TABLE `ALERTEVENTS` (
	Id INT NOT NULL, 
    AlertId INT NOT NULL, 
    CompInstanceId INT, 
    CompType VARCHAR(64), 
    AppInstanceId INT, 
    TxnId INT, 
    Metric varchar(64), 
    Severity INT not null, 
    Time Timestamp null, 
    ResponseTime varchar(64) not null, 
    AlertTitle varchar(64) not null default 'ComponentAlert', 
    Type varchar(16) default null, 
    TXNFlag varchar(32) DEFAULT 'none', 
    Component varchar(64) DEFAULT NULL, 
    Description varchar(256) DEFAULT NULL, 
    PRIMARY KEY(Id, AlertId), 
    KEY `index_AlertTitle_ALERTS`(`AlertTitle`), 
    KEY index_alerts_time (`Time`), 
    KEY CompAlertIndex (`CompInstanceId`, `Metric`, `Component`)
);

CREATE TABLE `view_application_info` (
	instanceid INT,
    name varchar(128),
    MaintainenanceFlag int
);

CREATE TABLE `view_app_comp_inst_ipaddress` (
	AppInstanceId        int(11),          
 	CompInstanceId       int(11) ,               
 	CompId               int(11)  ,     
 	MonitorFlag          int(11)   ,          
 	MonitorAvailability  int(11)    ,            
 	Status               int(11)     ,         
 	Type                 varchar(64)  ,      
 	Name                 varchar(64)   ,     
 	hostaddress          text          ,
 	port                 text           ,     
 	hostid               bigint(11)  
) ENGINE=InnoDB DEFAULT CHARSET=latin1; 

create database apmcommon;
use apmcommon;
grant all privileges on apmcommon.* to 'appsone'@'%';

CREATE TABLE `METRICDEF` (
  `CompMetricSetId` int(11) NOT NULL,
  `Id` int(11) NOT NULL,
  `Name` varchar(64) NOT NULL,
  `DataType` varchar(64) NOT NULL,
  `Threshold` int(11) NOT NULL,
  `AlertEnableFlag` int(11) NOT NULL,
  `AlertSeverity` int(11) NOT NULL,
  `Units` varchar(64) NOT NULL,
  `DoAnalytics` tinyint(1) NOT NULL,
  `ClusterOp` varchar(16) NOT NULL DEFAULT 'SUM',
  `Status` int(1) NOT NULL DEFAULT '1',
  `MetricDescription` varchar(1024)  

) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `incident_integration_kpi_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_name` varchar(512) NOT NULL,
  `application_name` varchar(512) NOT NULL,
  `component_name` varchar(128) NOT NULL,
  `kpi_name` varchar(128) NOT NULL,
  `kpi_type` enum('AVAILABILITY','STATIC','ANALYTICS','ONLINE') NOT NULL,
  `severity` enum('S1','S2') NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=317 DEFAULT CHARSET=latin1;

select * from ALERTS;
select * from ALERTEVENTS;

INSERT INTO `dataadapter`.`ALERTS` (`Id`, `AlertProfileId`, `Name`, `Value`, `CurrentTime`, `Severity`) VALUES ('6987', '1', 'MEM_UTIL', '45.9', '2020-04-24 02:46:00', '1');
INSERT INTO `dataadapter`.`ALERTS` (`Id`, `AlertProfileId`, `Name`, `Value`, `CurrentTime`, `Severity`) VALUES ('6988', '1', 'CPU_UTIL', '82.1', '2020-04-24 02:49:00', '1');
INSERT INTO `dataadapter`.`ALERTS` (`Id`, `AlertProfileId`, `Name`, `Value`, `CurrentTime`, `Severity`) VALUES ('6989', '1', 'Disk_Read', '8.3', '2020-04-24 02:57:00', '1');

INSERT INTO `dataadapter`.`ALERTEVENTS` (`Id`, `AlertId`, `CompInstanceId`, `CompType`, `AppInstanceId`, `TxnId`, `Metric`, `Severity`, `Time`, `ResponseTime`, `AlertTitle`, `Component`, `Description`) VALUES ('1', '6987', '8', 'CUSTOM', '0', '0', 'MEM_UTIL', '1', '2020-04-24 02:46:00', '818', 'KPI_Alert', 'STATIC', 'KPI_Alerts');
INSERT INTO `dataadapter`.`ALERTEVENTS` (`Id`, `AlertId`, `CompInstanceId`, `CompType`, `AppInstanceId`, `TxnId`, `Metric`, `Severity`, `Time`, `ResponseTime`, `AlertTitle`, `Component`, `Description`) VALUES ('1', '6988', '8', 'CUSTOM', '0', '0', 'CPU_UTIL', '1', '2020-04-24 02:49:00', '7.18', 'KPI_Alert', 'STATIC', 'KPI_Alerts');
INSERT INTO `dataadapter`.`ALERTEVENTS` (`Id`, `AlertId`, `CompInstanceId`, `CompType`, `AppInstanceId`, `TxnId`, `Metric`, `Severity`, `Time`, `ResponseTime`, `AlertTitle`, `Component`, `Description`) VALUES ('1', '6989', '8', 'CUSTOM', '0', '0', 'Disk_Read', '1', '2020-04-24 02:57:00', '9.318', 'KPI_Alert', 'STATIC', 'KPI_Alerts');

select * from view_application_info;
insert into `dataadapter`.`view_application_info` (`instanceid`, `name`, `MaintainenanceFlag`) values (1, 'MEAP Transaction Mapper', 1);

select * from view_app_comp_inst_ipaddress;
insert into `dataadapter`.`view_app_comp_inst_ipaddress` (`AppInstanceId`, `CompInstanceId`, `CompId`, `MonitorFlag`, `MonitorAvailability`, `Status`, `Type`, `Name`) 
			values (1, 8, 45, 1, 1, 1, 'CUSTOM', 'DBPR02');
            
select * from COMPONENTSTATICTHRESHOLDDEF;
INSERT INTO `dataadapter`.`COMPONENTSTATICTHRESHOLDDEF` (`ComponentInstanceId`, `ComponentId`, `KPIId`, `Severity`, `KPIName`, `Operation`, `Min`, `Max`) VALUES ('1', '45', '1', '1', 'CPU_UTIL', 'greater than', '1', '0');
INSERT INTO `dataadapter`.`COMPONENTSTATICTHRESHOLDDEF` (`ComponentInstanceId`, `ComponentId`, `KPIId`, `Severity`, `KPIName`, `Operation`, `Min`, `Max`) VALUES ('1', '45', '2', '1', 'Disk_Read', 'greater than', '1', '0');
INSERT INTO `dataadapter`.`COMPONENTSTATICTHRESHOLDDEF` (`ComponentInstanceId`, `ComponentId`, `KPIId`, `Severity`, `KPIName`, `Operation`, `Min`, `Max`) VALUES ('1', '45', '3', '1', 'MEM_UTIL', 'greater than', '1', '0');

insert into `apmcommon`.`METRICDEF` (`CompMetricSetId`,`Id`, `Name` , `DataType`, `Threshold`, `AlertEnableFlag`,`AlertSeverity` ,`Units`,  `DoAnalytics`, `MetricDescription`) VALUES (1, 1, 'CPU_UTIL', 'FLOAT', 5, 1, 1, 'Percentage', 1, 'CPU %');
insert into `apmcommon`.`METRICDEF` (`CompMetricSetId`,`Id`, `Name` , `DataType`, `Threshold`, `AlertEnableFlag`,`AlertSeverity` ,`Units`,  `DoAnalytics`, `MetricDescription`) VALUES (1, 2, 'Disk_Read', 'FLOAT', 5, 1, 1, 'Percentage', 1, 'Disk_Read %');
insert into `apmcommon`.`METRICDEF` (`CompMetricSetId`,`Id`, `Name` , `DataType`, `Threshold`, `AlertEnableFlag`,`AlertSeverity` ,`Units`,  `DoAnalytics`, `MetricDescription`) VALUES (1, 3, 'MEM_UTIL', 'FLOAT', 5, 1, 1, 'Percentage', 1, 'MEM %');
 

select * from apmcommon.METRICDEF; 
select * from apmcommon.incident_integration_kpi_mapping;
delete from apmcommon.incident_integration_kpi_mapping;
INSERT INTO `apmcommon`.`incident_integration_kpi_mapping` (`id`, `enterprise_name`, `application_name`, `component_name`, `kpi_name`, `kpi_type`, `severity`) VALUES ('1', 'Credit Suisse', 'MEAP Transaction Mapper', 'DBPR02', '*', 'STATIC', 'S2');
-- OR
INSERT INTO `apmcommon`.`incident_integration_kpi_mapping` (`id`, `enterprise_name`, `application_name`, `component_name`, `kpi_name`, `kpi_type`, `severity`) VALUES ('1', 'Credit Suisse', 'MEAP Transaction Mapper', 'DBPR02', 'Disk_Read,MEM_UTIL', 'STATIC', 'S2');


-- On 174
INSERT INTO `apmcommon`.`incident_integration_kpi_mapping` (`id`, `enterprise_name`, `application_name`, `component_name`, `kpi_name`, `kpi_type`, `severity`) VALUES ('1', 'CITI', 'MSTSFEAP.Application', 'DBPR02', 'Disk_Read,MEM_UTIL', 'STATIC', 'S2');
