<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/heal-connectors.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-connectors_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/heal-connectors-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-connectors-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.etladapter" level="INFO" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <root name="com.heal" level="INFO">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>