# Troubleshooting:

Cert Issues:

1.	echo | openssl s_client -connect grpc.appnomic:11000 1>&1 | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTFIFICATE-/p' > /opt/heal/ext-system-adapter/heal-grpc.cert
2.	Ensure the certificate name & path inside container matches yaml entry for heal-cert /opt/heal/ext-system-adapter/heal-grpc.cert
3.	keytool -import -trustcacerts -keystore cacerts -storepass changeit -noprompt -alias grpc.appnomic -file /opt/heal/ext-system-adapter/heal-grpc.cert 

If you get certificate errors, double check step no 2.

# Running RabbitMQ on Docker

1. Pull image:  docker pull rabbitmq:3.8-management
2. Creating container: docker run -d -p 15672:15672 -p 5672:5672 --name rabbitmqdev rabbitmq:3.8-management

# Project Setup using Eclipse

1.	Import Project into eclipse. 
2.	Ensure Maven is installed.
3.	Ensure that you are connected to HO network (via VPN, if reqd)
4.	Maven command to build: mvn clean install assembly:single

# Building the Docker image

1.	Ensure docker is installed on your machine
2.	Open docker/build.sh - update Maven path if required.
3.	Execute build.sh with a tag to be used when creating the docker image. For e.g., if your command is ./build.sh 1.0.3 It will create a tar of the docker image ext-system-adapter:1.0.3.tar with docker tag as 1.0.3

# Getting started

1.	Refer to junit.com.heal.connector.adapter.ConfigurationGenerator for examples on how to generate the configuration yaml for the adapter.
2.	Reference link: 
	-	https://appnomic.atlassian.net/wiki/spaces/VK/pages/1433141273/AppsOne+KPI+Adapter+for+Logscan+and+other+3rd+Party+Systems
	-	https://appnomic.atlassian.net/wiki/spaces/VK/pages/1217822759/Logscan+Design+Technical+Approach#Logscan:Design/TechnicalApproach-PushingKPIstoAppsOne
	
	
---------------------------------------------------------------------------------------------------------

1.	Logstash Impact of 2 Outputs
	-	CPU
		-	With: 180% with infrequent spikes uptil 600%  
			-	Incoming events rate: 2100
			-	Events emitted: 2100
			-	As per Kibana
				-	CPU Util: 14%
				-	System Load: 1.09

		-	Without: 300% - 600%
			-	Incoming events rate: 19k
			-	Events emitted: 19k
			-	As per Kibana
				-	CPU Util: 32%
				-	System Load: 18.86
				

2.	Elasticsearch impact of search
	-	Without Adapter
		-	CPU -- All elastic containers range between 40% to 60% with spikes to 300%
		-	Indexing Rate - Spikes between 5k and 20k
		-	Search Latency - Range between 1 ms to 4 ms
		-	Indexing latency - 0.4 ms

	-	LA
		-	Indexing Rate - Spikes between 5k and 20k
		-	Search Latency - Range between 1 ms to 4 ms
		-	Indexing latency - 0.4 ms
	-	LTM
		-	CPU
		-	Indexing Rate
		-	Search Latency
	-	BOTH
		-	CPU
		-	Indexing Rate
		-	Search Latency

