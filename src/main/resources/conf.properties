# ==========================================================
# RabbitMQ Configuration
# ==========================================================
spring.rabbitmq.addresses=**************:5671
spring.rabbitmq.username=guest
spring.rabbitmq.password=Z3Vlc3Q=
spring.rabbitmq.ssl.enabled=true
spring.rabbitmq.ssl.algorithm=TLSv1.2

connector.message.queue.name=multi-connector-1

# ==========================================================
# Mysql Database Configuration
# ==========================================================
spring.datasource.url=************************************************************************************************************************************************************************
spring.datasource.username=dbadmin
spring.datasource.password=cm9vdEAxMjM=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.minimum.idle.connections=100
spring.datasource.maximum.pool.size=200
spring.datasource.connection.timeout=30000
spring.datasource.connection.idle.timeout=600000
spring.datasource.max.life.time=1800000

# ==========================================================
# Redis Configuration
# ==========================================================
spring.redis.cluster.nodes=**************:7001,**************:7002,**************:7003,**************:7004,**************:7005,**************:7006
spring.redis.ssl=true
spring.redis.username=
spring.redis.password=cmVkaXNAMTIz
spring.redis.cluster.mode=true
spring.redis.max.idle.connections=500
spring.redis.min.idle.connections=500
spring.redis.max.total.connections=500
spring.redis.max.wait.secs=20
spring.redis.share.native.connection=true
spring.redis.txn.stream.name=txn-aggregated-data

# ==========================================================
# Health Metrics
# ==========================================================
health.metrics.update.interval.milliseconds=60000
health.metrics.log.interval.milliseconds=10000
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.enabled=true
management.endpoints.web.base-path=/measure
management.server.port=8989
spring.jmx.enabled=true

# ==========================================================
# Undertow HTTP2 Configuration
# ==========================================================
#server.http2.enabled=false
server.port=7443
#server.ssl.enabled-protocols=TLSv1.3
#server.ssl.key-store=/opt/adapter/appnomic-keystore.jks
#server.ssl.key-store-password=
#server.ssl.trust-store=/opt/adapter/appnomic-truststore.jks
#server.ssl.trust-store-password=

# =======================
# Keycloak Configurations
# =======================
keycloak.host=**************
keycloak.port=8443
keycloak.username=appsoneadmin
keycloak.password=QXBwc29uZUAxMjM=

# ==========================================================
# RMQ Configuration
# ==========================================================

anomaly.queue.name=anomaly-event-action-messages
anomaly.anomalyOutputMLESignalQueueName.name=anomaly-event-mle-signal-messages
anomaly.anomalySignalQueueName.name=anomaly-event-signal-messages