package com.heal.etladapter.repo.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicAgentEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class AgentRepo {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public  List<BasicAgentEntity> getAccountWiseAgents(String accountIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/agents";
            String hash = "ACCOUNTS_" + accountIdentifier + "_AGENTS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hash);

            if (obj == null) {
                log.error("Agents unavailable for account: {}", accountIdentifier);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching configuration for account: {}. Details: ", accountIdentifier, e);
            return new ArrayList<>();
        }
    }
}
