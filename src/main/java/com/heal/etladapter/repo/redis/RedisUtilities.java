package com.heal.etladapter.repo.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.ApplicationSettings;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class RedisUtilities {

    @Autowired
    public RedisTemplate<String, Object> redisTemplate;
    @Autowired
    public ObjectMapper objectMapper;


    public List<ViewTypes> getMstTypes() {
        try {
            String key = "/heal/types";
            String hashKey = "HEAL_TYPES";

            Object mstTypes = redisTemplate.opsForHash().get(key, hashKey);

            if (mstTypes == null) {
                log.error("Master Types information unavailable for Heal.");
                return new ArrayList<>();
            }

            return objectMapper.readValue(mstTypes.toString(), new TypeReference<List<ViewTypes>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Master Types information for Heal.", e);
            return new ArrayList<>();
        }
    }

    public ApplicationSettings getApplicationSettings(String accountIdentifier, String applicationIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/settings";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_SETTINGS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Application settings unavailable for application [{}] mapped to account [{}]", applicationIdentifier, accountIdentifier);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<ApplicationSettings>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting application settings for application [{}] mapped to account [{}].", applicationIdentifier, accountIdentifier, e);
            return null;
        }
    }
}
