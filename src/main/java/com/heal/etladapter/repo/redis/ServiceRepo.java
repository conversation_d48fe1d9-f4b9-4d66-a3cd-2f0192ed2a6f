package com.heal.etladapter.repo.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Rule;
import com.heal.configuration.pojos.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class ServiceRepo {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public Service getServiceDetails(String accountIdentifier, String serviceIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
            String hash = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;

            Object obj = redisTemplate.opsForHash().entries(key).get(hash);

            if (obj == null) {
                log.error("Service details unavailable in redis for account: {}, service: {}, ", accountIdentifier, serviceIdentifier.toLowerCase());
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching Service for account: {}, service: {}. Details: ", accountIdentifier, serviceIdentifier.toLowerCase(), e);
            return null;
        }
    }

    public List<BasicEntity> getApplicationForService(String accountIdentifier, String serviceIdentifier) {

        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/applications";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_APPLICATIONS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("ServiceWiseApplications unavailable in redis for account: {}, service: {}, ", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching ServiceWiseApplications for account: {}, service: {}. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Map<Integer, List<BasicEntity>> getInboundDetails(String accountIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/inbounds";
            String hash = "ACCOUNTS_" + accountIdentifier + "_INBOUNDS";
            Object obj = redisTemplate.opsForHash().entries(key).get(hash);
            if (obj == null) {
                log.error("InboundDetails unavailable in redis for account: {}", accountIdentifier);
                return new HashMap<>();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching InboundDetails for account: {}. Details: ", accountIdentifier, e);
            return new HashMap<>();
        }
    }

    public Map<Integer, List<BasicEntity>> getOutboundDetails(String accountIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/outbounds";
            String hash = "ACCOUNTS_" + accountIdentifier + "_OUTBOUNDS";
            Object obj = redisTemplate.opsForHash().entries(key).get(hash);
            if (obj == null) {
                log.error("OutboundDetails unavailable in redis for account: {}", accountIdentifier);
                return new HashMap<>();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching OutboundDetails for account: {}. Details: ", accountIdentifier, e);
            return new HashMap<>();
        }
    }

    public List<BasicAgentEntity> getAgentsForService(String accountIdentifier, String serviceIdentifier) {

        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/agents";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_AGENTS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("ServiceWiseAgents unavailable in redis for account: {}, service: {}, ", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching ServiceWiseAgents for account: {}, service: {}. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getAccountWiseServices(String accountIdentifier) {

        try {
            String key = "/accounts/" + accountIdentifier + "/services";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("AccountWiseServices unavailable in redis for account: {} to get services", accountIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching AccountWiseServices for account: {}, Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<Rule> getServiceWiseRules(String accountIdentifier, String serviceIdentifier) {

        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/rules";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_RULES";


            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("ServiceWiseRules unavailable in redis for account: {}, service : {}", accountIdentifier, serviceIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching ServiceWiseRules for account: {}, Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }
}
