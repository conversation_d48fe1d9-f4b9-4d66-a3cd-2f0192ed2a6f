package com.heal.etladapter.repo.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Repository
public class InstanceRepo {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public CompInstClusterDetails getInstanceDetails(String accountIdentifier, String instanceIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier;
            String hash = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier;

            Object obj = redisTemplate.opsForHash().entries(key).get(hash);

            if (obj == null) {
                log.error("Configuration unavailable for account: {}, instance identifier: {}, ", accountIdentifier, instanceIdentifier);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching configuration for account: {}, instance identifier: {}. Details: ", accountIdentifier, instanceIdentifier, e);
            return null;
        }
    }

    public List<BasicKpiEntity> getComponentKPIDetails(String accountIdentifier, String component) {
        try {
            String key = "/accounts/" + accountIdentifier + "/components/" + component + "/kpis";
            String hash = "ACCOUNTS_" + accountIdentifier + "_COMPONENTS_" + component + "_KPIS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hash);

            if (obj == null) {
                log.error("Configuration unavailable for account: {}, component: {}, ", accountIdentifier, component);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching configuration for account: {}, component: {}. Details: ", accountIdentifier, component, e);
            return new ArrayList<>();
        }
    }

    public List<BasicEntity> getInstanceWiseServices(String accountIdentifier, String instanceIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier + "/services";
            String hash = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier + "_SERVICES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hash);

            if (obj == null) {
                log.error("Instance wise services unavailable for account: {}, instance: {}, ", accountIdentifier, instanceIdentifier);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching instance wise services for account: {}, instance: {}. Details: ", accountIdentifier, instanceIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<CompInstClusterDetails> getInstancesByAccount(String accIdentifier) {
        String key = "/accounts/" + accIdentifier + "/instances";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_INSTANCES";
        try {
            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Account wise instances unavailable for account [{}]", accIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<List<CompInstClusterDetails>>() {
            });
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }
}
