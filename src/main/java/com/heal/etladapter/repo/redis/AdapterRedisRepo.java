package com.heal.etladapter.repo.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.connectors.ConnectorChainConfiguration;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class AdapterRedisRepo {

    //TODO: Add a bean to include properties to ignore unknown fields.
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private AdapterHealthMetrics healthMetrics;

    public ConnectorChainConfiguration getConnectorAdapterConfiguration(String jobId, String accountIdentifier, String connectorInstanceIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/connectors/" + connectorInstanceIdentifier.toLowerCase() + "/configuration";
            String hash = "ACCOUNTS_" + accountIdentifier + "_CONNECTORS_" + connectorInstanceIdentifier.toLowerCase() + "_CONFIGURATION";

            Object obj = redisTemplate.opsForHash().entries(key).get(hash);

            if (obj == null) {
                healthMetrics.updateRedisConnectionErrorCount();
                log.error("Configuration unavailable for jobId:{}, account:{}, connector instance:{}, ", jobId, accountIdentifier, connectorInstanceIdentifier.toLowerCase());
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            healthMetrics.updateRedisConnectionErrorCount();
            log.error("Error occurred while fetching configuration for jobId:{}, accountIdentifier:{}, connector instance:{}.", jobId, accountIdentifier, connectorInstanceIdentifier.toLowerCase(), e);
            return null;
        }
    }
}