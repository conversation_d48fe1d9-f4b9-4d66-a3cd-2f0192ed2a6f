package com.heal.etladapter.repo.mysql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class NameValueRepository {


    public void setPersistedNameValuePair(String name, String value, String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "REPLACE INTO domain_adapter_state (name, value, domain) VALUES (?, ?, ?)";

            jdbcTemplate.update(query, name, value, domain);
        } catch (Exception e) {
            log.error("Exception occurred while updating entry in domain_adapter_state. Details: name: {}, value: {}", name, value, e);
        }
    }

    public String getPersistedNameValuePair(String name, String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "SELECT value FROM domain_adapter_state where name = ? and domain = ?";

            return jdbcTemplate.queryForObject(query, String.class, name, domain);
        } catch (EmptyResultDataAccessException e) {
            log.error("Value unavailable for name {}", name);
            return null;
        } catch (Exception e) {
            log.error("Exception occurred while fetching account identifier. Details: ", e);
            return null;
        }
    }
}