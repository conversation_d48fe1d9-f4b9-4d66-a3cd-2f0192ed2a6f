package com.heal.etladapter.repo.mysql;

import com.heal.etladapter.pojos.HealAlert;
import com.heal.etladapter.utility.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class Heal48AlertsRepository {

    public List<HealAlert> getLatestAlerts(Date after, Date before, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select a.Id as healAlertId, a.CurrentTime as healAlertTimestamp, a.Name as KPIName, a.Value as KPIValue, " +
                    "ae.CompType as ComponentType, ae.Component as Component, a.Severity as Severity, ae.CompInstanceId as ComponentInstanceId " +
                    "from ALERTS a, ALERTEVENTS ae where a.Id = ae.AlertId and a.CurrentTime BETWEEN ? and ?";

            return jdbcTemplate.query(query, (rs, rowNum) -> HealAlert.builder()
                    .healAlertId(BigInteger.valueOf(rs.getInt("healAlertId")))
                    .alertTimestamp(rs.getDate("healAlertTimestamp"))
                    .kpiName(rs.getString("KPIName"))
                    .kpiValue(rs.getBigDecimal("KPIValue"))
                    .componentType(rs.getString("ComponentType"))
                    .component(rs.getString("Component"))
                    .componentInstanceId(rs.getInt("ComponentInstanceId"))
                    .severity(rs.getShort("Severity"))
                    .build(), DateHelper.date2stringConverter(after), DateHelper.date2stringConverter(before));
        } catch (EmptyResultDataAccessException e) {
            log.info("Heal alerts unavailable for duration {} to {}", before, after);
        } catch (Exception e) {
            log.error("Exception occurred while fetching heal alerts for duration {} to {}", before, after);
        }

        return Collections.emptyList();
    }

    public HealAlert updateComponentDetails(HealAlert alert, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select vah.Name, vah.Type, vah.AppInstanceId, vah.CompId from view_app_comp_inst_ipaddress vah where vah.CompInstanceId = ?";

            List<HealAlert> result = jdbcTemplate.query(query, (rs, rowNum) -> HealAlert.builder()

                    .componentInstanceName(rs.getString("vah.Name"))
                    .componentType(rs.getString("vah.Type"))
                    .applicationInstanceId(rs.getInt("vah.AppInstanceId"))
                    .componentId(rs.getInt("vah.CompId"))
                    .build(), alert.getComponentInstanceId());

            if (!result.isEmpty()) {
                alert.setComponentInstanceName(result.get(0).getComponentInstanceName());
                alert.setComponentType(result.get(0).getComponentType());
                alert.setApplicationInstanceId(result.get(0).getApplicationInstanceId());
                alert.setComponentId(result.get(0).getComponentId());
            }
        } catch (EmptyResultDataAccessException e) {
            log.info("Component details unavailable for heal alert {}", alert);
        } catch (Exception e) {
            log.error("Exception occurred while fetching component details for heal alert {}", alert);
        }

        return alert;
    }

    public HealAlert updateApplicationDetails(HealAlert alert, JdbcTemplate jdbcTemplate) {
        String query = "select va.name as ApplicationName from view_application_info va where va.instanceid = ?";
        List <String> result = jdbcTemplate.queryForList(query, String.class, alert.getApplicationInstanceId());
        if (!result.isEmpty()) {
            alert.setApplicationName(result.get(0));
        }
        return alert;
    }

    public HealAlert updateOperationDetails(HealAlert alert, JdbcTemplate jdbcTemplate) {
        String query = "select opt.Operation as Operation, opt.KPIId as kpiId from COMPONENTSTATICTHRESHOLDDEF opt where opt.ComponentId = ? and opt.KPIName = ?";


        List<Object[]> results = jdbcTemplate.queryForList(query,Object[].class, alert.getComponentId(), alert.getKpiName());

        if (!results.isEmpty()) {
            alert.setOperationType(results.get(0)[0].toString());
            alert.setKpiId(Integer.parseInt(results.get(0)[1].toString()));
        }
        return alert;
    }

    public HealAlert updateUnitsDetails(HealAlert alert, JdbcTemplate jdbcTemplate) {
        String query = "select distinct Units from apmcommon.METRICDEF where Name = ?";

        List<String> results = jdbcTemplate.queryForList(query, String.class, alert.getKpiName());

        if (!results.isEmpty()) {
            alert.setUnits(results.get(0));
        }
        return alert;
    }

    public Boolean ruleExists(HealAlert alert, JdbcTemplate jdbcTemplate) {


        Boolean ruleExists = Boolean.FALSE;

        String query = "select kpi_name from apmcommon.incident_integration_kpi_mapping where enterprise_name = ? and application_name = ? and component_name = ? and kpi_type = ?";

        List<String> results = jdbcTemplate.queryForList(query, String.class, alert.getAccountName(), alert.getApplicationName(), alert.getComponentInstanceName(), alert.getKpiType());

        if (!results.isEmpty()) {
            String kpiName = results.get(0);
            if (kpiName.equals("*") || kpiName.toUpperCase().contains(alert.getKpiName().toUpperCase())) {
                ruleExists = Boolean.TRUE;
            }
        }
        return ruleExists;
    }

    private Boolean isAvailabilityKpi(String kpiName, Integer kpiId, JdbcTemplate jdbcTemplate) {

        Boolean ruleExists = Boolean.FALSE;
        String query = "select Id from apmcommon.AVAILABILITYMETRICDEF where Id = ? and Name = ? ";
        List<Integer> results = jdbcTemplate.queryForList(query, Integer.class, kpiId, kpiName);

        if (!results.isEmpty()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean isStaticKpi(String kpiName, Integer kpiId, JdbcTemplate jdbcTemplate) {

        String query = "select Id from apmcommon.METRICDEF where Id = ? and Name = ? ";
        List<Integer> results = jdbcTemplate.queryForList(query, Integer.class, kpiId, kpiName);

        if (!results.isEmpty()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public String getKpiType(String kpiName, Integer kpiId, JdbcTemplate jdbcTemplate) {
        if (kpiName == null || kpiName.isEmpty()) {
            return null;
        } else if (isStaticKpi(kpiName, kpiId, jdbcTemplate)) {
            return "STATIC";
        } else if (isAvailabilityKpi(kpiName, kpiId, jdbcTemplate)) {
            return "AVAILABILITY";
        }
        return "NA";

    }

}
