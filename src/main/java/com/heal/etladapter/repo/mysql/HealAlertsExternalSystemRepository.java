package com.heal.etladapter.repo.mysql;

import com.heal.etladapter.beans.HealAlertExternalSystemMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.List;

@Slf4j
@Component
public class HealAlertsExternalSystemRepository {

	public Boolean healAlertExists(BigInteger healAlertId, JdbcTemplate jdbcTemplate) {
		boolean alertExists = false;
		try {
			String query = "SELECT heal_alert_id, ext_id, ext_system, ext_system_response from a1_adapter_loaded_alerts where healAlertId = ?";

			List<HealAlertExternalSystemMapping> allAlerts = jdbcTemplate.query(query, (rs, rowNum) -> HealAlertExternalSystemMapping.builder()
					.healAlertId(BigInteger.valueOf(rs.getInt("heal_alert_id")))
					.externalSystemId(rs.getString("ext_id"))
					.externalSystem(rs.getString("ext_system"))
					.externalSystemResponse(rs.getString("ext_system_response"))
					.build(),healAlertId);

			if (!allAlerts.isEmpty()) {
				alertExists = true;
				log.info("Alert with ID {} already exists", healAlertId);
			}

		} catch (Exception hex) {
			log.error("Error fetching latest alerts: ", hex);
		}

		return alertExists;
	}
	public void save(HealAlertExternalSystemMapping healAlertExternalSystemMapping, JdbcTemplate jdbcTemplate) {

		try {
			String query = "INSERT INTO a1_adapter_loaded_alerts (heal_alert_id, ext_id,ext_system, ext_system_response) VALUES (:healAlertId, :externalSystemId, :externalSystem, :externalSystemResponse)"
					+ "ON DUPLICATE KEY UPDATE heal_alert_id = :healAlertId, ext_id = :externalSystemId,ext_system = :externalSystem ,ext_system_response = :externalSystemResponse";

			SqlParameterSource namedParameters = new BeanPropertySqlParameterSource(healAlertExternalSystemMapping);
			jdbcTemplate.update(query, namedParameters);
		} catch (Exception e) {
			// Handle exception
			log.error("Exception while inserting data into a1_adapter_loaded_alerts. Details: {}", healAlertExternalSystemMapping, e);
		}
	}

}
