package com.heal.etladapter.repo.mysql;

import com.heal.etladapter.beans.HealNodeCreationPayload;
import com.heal.etladapter.beans.KubernetesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class KubernetesRepo {


    //TODO: KRK implement the query
    public KubernetesConfig getKubernetesClusterConnectionDetails(String chainIdentifier, JdbcTemplate jdbcTemplate) {
        return null;
    }

    //TODO: KRK implement the query
    public HealNodeCreationPayload getNodeCreationPayload(String chainIdentifier, JdbcTemplate jdbcTemplate) {
        return null;
    }
}
