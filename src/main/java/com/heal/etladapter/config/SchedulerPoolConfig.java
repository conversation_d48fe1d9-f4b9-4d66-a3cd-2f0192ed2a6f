package com.heal.etladapter.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Slf4j
@Configuration
public class SchedulerPoolConfig {

    @Value("${scheduler.thread.pool.max.size:50}")
    private int maxPoolSize;

    @Bean("SchedulerPoolTaskExecutor")
    public ThreadPoolTaskScheduler getAsyncExecutor() {
        log.info("Setting up thread poolTaskScheduler");

        ThreadPoolTaskScheduler poolTaskScheduler = new ThreadPoolTaskScheduler();
        poolTaskScheduler.setPoolSize(maxPoolSize);
        poolTaskScheduler.setThreadNamePrefix("Connector-Scheduler-");

        return poolTaskScheduler;
    }
}
