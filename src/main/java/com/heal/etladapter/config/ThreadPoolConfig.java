package com.heal.etladapter.config;

import com.heal.etladapter.beans.AdapterHealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Value("${thread.pool.core.size:10}")
    private int corePoolSize;

    @Value("${thread.pool.max.size:20}")
    private int maxPoolSize;

    @Value("${thread.pool.queue.capacity:5000}")
    private int queueCapacity;
    @Autowired
    AdapterHealthMetrics healthMetrics;

    @Bean("ThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {
        log.info("Setting up thread executor");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setThreadNamePrefix("Connector-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(queueCapacity);
        executor.setRejectedExecutionHandler((r, e) -> {
            log.warn("Task rejected from thread pool executor.");
            healthMetrics.updateThreadPoolTaskExecutorRejectedCount(1);
            new ThreadPoolExecutor.CallerRunsPolicy().rejectedExecution(r, e);
        });
        executor.initialize();

        return executor;
    }

}
