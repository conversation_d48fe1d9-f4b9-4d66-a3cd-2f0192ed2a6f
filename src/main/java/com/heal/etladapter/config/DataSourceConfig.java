package com.heal.etladapter.config;

import com.heal.etladapter.utility.AdapterConstants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Configuration
public class DataSourceConfig {
    private final Map<String, JdbcTemplate> chainWorkerJdbcTemplateClientMap = new ConcurrentHashMap<>();

    public JdbcTemplate getOrCreateJdbcTemplate(String chainWorkerIdentifier, Map<String, String> parameters) {
        log.info("Get Jdbc template connection client for chainWorkerIdentifier: {}", chainWorkerIdentifier);

        return chainWorkerJdbcTemplateClientMap.compute(chainWorkerIdentifier, (k, existingJdbcTemplate) -> {
            if (existingJdbcTemplate != null) {
                log.debug("Jdbc template is already initialized for key:{}", chainWorkerIdentifier);
                return existingJdbcTemplate;
            }

            log.warn("Jdbc template is null! Need to re-initialize, key:{}", chainWorkerIdentifier);

            try {
                HikariDataSource hikariDataSource = getDataSourceConfig(parameters);
                return new JdbcTemplate(hikariDataSource);

            } catch (Exception e) {
                log.error("Failed to create Jdbc template for chainWorkerIdentifier:{}", chainWorkerIdentifier);
            }

            return null;
        });

    }

    public HikariDataSource getDataSourceConfig(Map<String, String> parameters) {

        String url = parameters.get(AdapterConstants.DATASOURCE_URL);
        String username = parameters.get(AdapterConstants.DATASOURCE_USERNAME);
        String password = parameters.get(AdapterConstants.DATASOURCE_PASSWORD);
        String driverClassName = parameters.get(AdapterConstants.DATASOURCE_DRIVER_CLASS_NAME);
        String minIdleConnections = parameters.getOrDefault(AdapterConstants.DATASOURCE_MIN_IDLE_CONNECTIONS, "100");
        String maxPoolSize = parameters.getOrDefault(AdapterConstants.DATASOURCE_MAX_POOL_SIZE, "200");
        String connectionTimeout = parameters.getOrDefault(AdapterConstants.DATASOURCE_CONNECTION_TIMEOUT, "5000");
        String connectionIdleTimeout = parameters.getOrDefault(AdapterConstants.DATASOURCE_CONNECTION_IDLE_TIMEOUT, "5000");
        String maxLifeTime = parameters.getOrDefault(AdapterConstants.DATASOURCE_MAX_LIFE_TIME, "180000");

        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setMinimumIdle(Integer.parseInt(minIdleConnections));
        hikariConfig.setMaximumPoolSize(Integer.parseInt(maxPoolSize));
        hikariConfig.setConnectionTimeout(Integer.parseInt(connectionTimeout));
        hikariConfig.setMaxLifetime(Integer.parseInt(maxLifeTime));
        hikariConfig.setIdleTimeout(Integer.parseInt(connectionIdleTimeout));
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
        hikariConfig.setDriverClassName(driverClassName);

        HikariDataSource source = new HikariDataSource(hikariConfig);

        log.info("source: {}", source);

        return source;
    }
}

