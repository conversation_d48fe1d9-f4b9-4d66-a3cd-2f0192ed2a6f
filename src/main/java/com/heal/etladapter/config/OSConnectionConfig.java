/* saga<PERSON> created on 25/03/24 inside the package - com.appnomic.heal.etladapter.config */
package com.heal.etladapter.config;

import com.appnomic.appsone.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.opensearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class OSConnectionConfig {
    private String nodes;
    private String username;
    private String password;
    private String protocol;
    private int maxConnectionsPerRoute;
    private int maxConnections;
    private int connectionTimeOutInSecs;
    private int socketTimeOutInSecs;
    private RestHighLevelClient client;


    public void initializeConnectionParams(Map<String, String> parameters) {
        try {
            nodes = parameters.get("opensearch.nodes");
            protocol = parameters.get("opensearch.protocol");
            username = parameters.get("opensearch.username");
            password = parameters.get("opensearch.password");
            maxConnectionsPerRoute = Integer.parseInt(parameters.get("opensearch.max.connections.per.route"));
            maxConnections = Integer.parseInt(parameters.get("opensearch.max.connections.total"));
            connectionTimeOutInSecs = Integer.parseInt(parameters.get("opensearch.connection.timeout.sec"));
            socketTimeOutInSecs = Integer.parseInt(parameters.get("opensearch.connection.socket.timeout.secs"));

            log.info("initializeConnectionParams OS Client configuration. Nodes:{}, protocol:{}, maxConnectionsPerRoute:{}, maxConnections:{}, connectionTimeoutInSecs:{}, sockeTimeOutInSecs:{}, username:{}",
                    nodes, protocol, maxConnectionsPerRoute, maxConnections, connectionTimeOutInSecs, socketTimeOutInSecs, username);
        } catch (Exception e) {
            log.error("Exception in getting worker parameters. Parameters:{}", parameters, e);
        }
    }

    private void pingOpenSearch() {
        try {
            if (client == null) {
                log.warn("Open search Client is null! Need to re-initialize");
            } else if (!client.ping(RequestOptions.DEFAULT)) {
                log.warn("Ping failed! Need to re-initialize");
            } else {
                // Ping worked, so connection is alive
                log.debug("Client is initialized! for {}", client);
                return;
            }
            if (client != null) {
                log.info("Closing old instance of invalid OS client. Will be creating a new client");
                client.close();
            }

            log.debug("Attempting to re-establish broken connection with OpenSearch - {}//{}", protocol, nodes);
            client = getOpenSearchClientConnection(username, password, nodes, protocol);
            log.debug("Connection established with OpenSearch");
        } catch (Exception iox) {
            log.error("{} occurred when connecting to {}://{}", iox.getClass(), protocol, nodes, iox);
            if (client != null) {
                try {
                    client.close();
                    log.warn("Previous connection closed. {}://{},", protocol, nodes);
                } catch (IOException e) {
                    //do nothing
                }
                client = null;
                log.error("{} occurred when connecting to {}://{}", iox.getClass(), protocol, nodes, iox);
            }
        }
    }

    public RestHighLevelClient getElasticClient(Map<String, String> parameters) {
        if (client == null) {
            initializeConnectionParams(parameters);
            pingOpenSearch();
            return client;
        }

        pingOpenSearch();
        return client;
    }

    private RestHighLevelClient getOpenSearchClientConnection(String username, String password, String nodes, String protocol) {

        RestHighLevelClient client = null;
        log.info("Attempting to establish connection with OpenSearch nodes [{}] with protocol:{}," +
                " username:{}, maxConnectionsPerRoute:{}, maxConnections:{} ", nodes, protocol, username, maxConnectionsPerRoute, maxConnections);
        try {
            List<String> nodesList = Arrays.asList(nodes.split(","));
            List<HttpHost> httpHosts = new ArrayList<>();
            nodesList.forEach(c -> {
                try {
                    String[] hostAndPort = c.split(":");
                    int port = Integer.parseInt(hostAndPort[1]);
                    HttpHost httpHost = new HttpHost(hostAndPort[0], port, protocol);
                    httpHosts.add(httpHost);
                } catch (Exception e) {
                    log.error("Invalid OpenSearch host and port details, Host: {}", c, e);
                }
            });
            if (httpHosts.isEmpty()) {
                log.error("Http hosts list is empty.");
                return null;
            }

            RestClientBuilder restClientBuilder = RestClient.builder(httpHosts.toArray(new HttpHost[0]));
            if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(password)) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                String decryptedPwd = new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8);
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, decryptedPwd));

                restClientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder ->
                                httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                                        .setMaxConnPerRoute(maxConnectionsPerRoute)
                                        .setMaxConnTotal(maxConnections))
                        .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                                .setConnectTimeout(connectionTimeOutInSecs * 1000).setSocketTimeout(socketTimeOutInSecs * 1000));
            } else {
                restClientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder ->
                                httpAsyncClientBuilder.setMaxConnPerRoute(maxConnectionsPerRoute)
                                        .setMaxConnTotal(maxConnections))
                        .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                                .setConnectTimeout(connectionTimeOutInSecs * 1000).setSocketTimeout(socketTimeOutInSecs * 1000));
            }
            client = new RestHighLevelClient(restClientBuilder);
            log.info("Established connection with OpenSearch nodes [{}] with protocol:{}, username:{}, maxConnectionsPerRoute:{}, maxConnections:{}.",
                    nodes, protocol, username, maxConnectionsPerRoute, maxConnections);
        } catch (Exception e) {
            log.error("Error while establishing connection to OpenSearch nodes [{}], Protocol:{}, username:{}, maxConnectionsPerRoute:{}, maxConnections:{}",
                    nodes, protocol, username, maxConnectionsPerRoute, maxConnections, e);
        }
        return client;
    }
}
