package com.heal.etladapter.config;

import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.appnomic.appsone.util.KeyCloakConnectionSpec;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Base64;

@Slf4j
@Configuration
public class KeycloakConfig {
    @Value("${keycloak.host:keycloak.appnomic}")
    private String host;
    @Value("${keycloak.port:8443}")
    private String port;
    @Value("${keycloak.username:}")
    private String username;
    @Value("${keycloak.password:}")
    private String password;

    // This method will be invoked after the KeycloakConfig bean is initialized
    @PostConstruct
    public void initializeKeycloakConnection() {
        log.info("Initializing Keycloak connection...");

        if (isKeyCloakConnectionManagerConfigured()) {
            log.info("Keycloak connection successfully initialized.");
        } else {
            log.error("Failed to initialize Keycloak connection. Some configuration parameters are missing.");
        }
    }

    public boolean isKeyCloakConnectionManagerConfigured() {
        if (isNullOrEmpty(host, AdapterConstants.KC_HOST) || isNullOrEmpty(port, AdapterConstants.KC_PORT)
                || isNullOrEmpty(username, AdapterConstants.KC_USERNAME) || isNullOrEmpty(password, AdapterConstants.KC_PASSWORD)) {
            return false;
        }
        String decodedPassword = new String(Base64.getDecoder().decode(password));
        KeyCloakConnectionSpec keyCloakConnectionSpec = new KeyCloakConnectionSpec()
                .setKeyCloakIP(host)
                .setKeyCloakPort(port)
                .setKeyCloakUsername(username)
                .setKeyCloakPassword(decodedPassword)
                .createKeyCloakConnectionSpec();
        KeycloakConnectionManager.setKeyCloakConnectionSpec(keyCloakConnectionSpec);
        return true;
    }

    private boolean isNullOrEmpty(String obj, String configParam) {
        if (obj == null) {
            log.error("{} configuration parameter in invalid", configParam);
            return true;
        }
        return obj.isEmpty();
    }

}
