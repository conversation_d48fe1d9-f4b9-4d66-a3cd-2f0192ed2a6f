package com.heal.etladapter.config;

import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.utility.AdapterConstants;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.api.StatefulConnection;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.protocol.ProtocolVersion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Configuration
@Slf4j
public class RedisConnectionConfig {

    @Value("${spring.redis.cluster.nodes:redis-node1.appnomic:9001}")
    private List<String> clusterNodes;

    @Value("${spring.redis.ssl:true}")
    private boolean sslEnabled;

    @Value("${spring.redis.max.idle.connections:1000}")
    private int maxIdleConnections;

    @Value("${spring.redis.min.idle.connections:800}")
    private int minIdleConnections;

    @Value("${spring.redis.max.total.connections:1000}")
    private int maxTotalConnections;

    @Value("${spring.redis.max.wait.sec:20}")
    private int maxWaitTimeInSecs;

    @Value("${spring.redis.username:}")
    private String username;

    @Value("${spring.redis.password:}")
    private String password;

    @Value("${spring.redis.share.native.connection:false}")
    private boolean shareNativeConnection;

    @Value("${spring.redis.cluster.mode:true}")
    private boolean redisClusterMode;
    @Autowired
    private AdapterHealthMetrics healthMetrics;

    private final ConcurrentMap<String, RedisTemplate<String, Object>> chainWorkerRedisClientMap = new ConcurrentHashMap<>();

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        log.info("Factory method called with cluster mode as {}", redisClusterMode);

        LettuceConnectionFactory factory;
        if (redisClusterMode) {
            RedisClusterConfiguration configuration = new RedisClusterConfiguration(clusterNodes);

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            factory = new LettuceConnectionFactory(configuration, lettucePoolConfig());
        } else {
            RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            String[] hostAndPort = clusterNodes.get(0).split(":");

            configuration.setHostName(hostAndPort[0]);
            configuration.setPort(Integer.parseInt(hostAndPort[1]));

            factory = new LettuceConnectionFactory(configuration, lettucePoolConfig());
        }

        if (sslEnabled) {
            factory.isUseSsl();
        }

        factory.setShareNativeConnection(shareNativeConnection);
        factory.setValidateConnection(false);
        factory.afterPropertiesSet();

        return factory;
    }

    @Bean
    LettucePoolingClientConfiguration lettucePoolConfig() {
        log.debug("Lettuce pool config method called.");
        GenericObjectPoolConfig<StatefulConnection<?, ?>> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxIdle(maxIdleConnections);
        poolConfig.setMinIdle(minIdleConnections);
        poolConfig.setMaxWait(Duration.ofSeconds(maxWaitTimeInSecs));
        poolConfig.setMaxTotal(maxTotalConnections);

        ClientOptions clientOptions;
        if (redisClusterMode) {
            clientOptions = ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP3)
                    .validateClusterNodeMembership(true)
                    .maxRedirects(5)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                            .enablePeriodicRefresh()
                            .build())
                    .build();
        } else {
            clientOptions = ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP2)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .build();
        }


        if (sslEnabled) {
            return LettucePoolingClientConfiguration.builder()
                    .poolConfig(poolConfig)
                    .commandTimeout(Duration.ofSeconds(maxWaitTimeInSecs))
                    .clientOptions(clientOptions)
                    .useSsl()
                    .build();
        }

        return LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .commandTimeout(Duration.ofSeconds(maxWaitTimeInSecs))
                .clientOptions(clientOptions)
                .build();
    }

    public RedisTemplate<String, Object> getRedisTemplateConnection(String connectorInstanceIdentifier, Map<String, String> parameters) {
        log.info("Get redis template connection client for chainWorkerIdentifier: {}", connectorInstanceIdentifier);
        return chainWorkerRedisClientMap.compute(connectorInstanceIdentifier, (k, existingRedisClient) -> {
            if (existingRedisClient != null) {
                log.debug("Redis Client is already initialized for key:{}", connectorInstanceIdentifier);
                return existingRedisClient;
            }

            log.warn("Redis client is null! Need to re-initialize, key:{}", connectorInstanceIdentifier);

            try {
                RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
                RedisConnectionFactory redisConnectionFactory = getRedisConnectionFactory(connectorInstanceIdentifier, parameters);

                if (redisConnectionFactory == null) {
                    log.error("Failed to create redis connection factory for connector instance: {}", connectorInstanceIdentifier);
                    return null;
                }

                redisTemplate.setConnectionFactory(redisConnectionFactory);
                redisTemplate.setKeySerializer(new StringRedisSerializer());
                redisTemplate.setHashKeySerializer(new StringRedisSerializer());
                redisTemplate.setValueSerializer(new StringRedisSerializer());
                redisTemplate.setHashValueSerializer(new StringRedisSerializer());
                redisTemplate.setEnableTransactionSupport(true);
                redisTemplate.afterPropertiesSet();

                return redisTemplate;

            } catch (Exception e) {
                log.error("Failed to create redis client for chainWorkerIdentifier:{}", connectorInstanceIdentifier);
                healthMetrics.updateRedisConnectionErrorCount();
            }

            return null;
        });

    }

    private RedisConnectionFactory getRedisConnectionFactory(String connectorInstanceIdentifier, Map<String, String> parameters) {
        log.info("Factory method called with cluster mode as {}", redisClusterMode);

        LettucePoolingClientConfiguration lettucePoolingClientConfiguration = lettucePoolConfig(connectorInstanceIdentifier, parameters);
        if (lettucePoolingClientConfiguration == null) {
            log.error("Error while creating lettuce pooling client configuration for chainWorkerIdentifier: {}", connectorInstanceIdentifier);
            return null;
        }

        LettuceConnectionFactory factory;

        if (redisClusterMode) {
            RedisClusterConfiguration configuration = new RedisClusterConfiguration(clusterNodes);

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            factory = new LettuceConnectionFactory(configuration, lettucePoolingClientConfiguration);
        } else {
            RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            String[] hostAndPort = clusterNodes.get(0).split(":");

            configuration.setHostName(hostAndPort[0]);
            configuration.setPort(Integer.parseInt(hostAndPort[1]));

            factory = new LettuceConnectionFactory(configuration, lettucePoolingClientConfiguration);
        }

        if (sslEnabled) {
            factory.isUseSsl();
        }

        factory.setShareNativeConnection(shareNativeConnection);
        factory.setValidateConnection(false);
        factory.afterPropertiesSet();

        return factory;
    }

    private LettucePoolingClientConfiguration lettucePoolConfig(String chainWorkerIdentifier, Map<String, String> parameters) {
        log.info("Lettuce pool config method called for chain: {}.", chainWorkerIdentifier);

        String redisMaxIdleConnections = parameters.getOrDefault(AdapterConstants.REDIS_MAX_IDLE_CONNECTIONS, Integer.toString(maxIdleConnections));
        String redisMinIdleConnections = parameters.getOrDefault(AdapterConstants.REDIS_MIN_IDLE_CONNECTIONS, Integer.toString(minIdleConnections));
        String redisMaxWaitTimeInSecs = parameters.getOrDefault(AdapterConstants.REDIS_MAX_WAIT_TIME_IN_SECS, Integer.toString(maxWaitTimeInSecs));
        String redisMaxTotalConnections = parameters.getOrDefault(AdapterConstants.REDIS_MAX_TOTAL_CONNECTIONS, Integer.toString(maxTotalConnections));

        try {
            GenericObjectPoolConfig<StatefulConnection<?, ?>> poolConfig = new GenericObjectPoolConfig<>();
            poolConfig.setMaxIdle(Integer.parseInt(redisMaxIdleConnections));
            poolConfig.setMinIdle(Integer.parseInt(redisMinIdleConnections));
            poolConfig.setMaxWait(Duration.ofSeconds(Integer.parseInt(redisMaxWaitTimeInSecs)));
            poolConfig.setMaxTotal(Integer.parseInt(redisMaxTotalConnections));

            log.debug("Lettuce pool config details for chain: {}, {},", chainWorkerIdentifier, poolConfig);

            ClientOptions clientOptions;
            if (redisClusterMode) {
                clientOptions = ClusterClientOptions.builder()
                        .autoReconnect(true)
                        .protocolVersion(ProtocolVersion.RESP3)
                        .validateClusterNodeMembership(true)
                        .maxRedirects(5)
                        .cancelCommandsOnReconnectFailure(true)
                        .pingBeforeActivateConnection(true)
                        .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                                .enablePeriodicRefresh()
                                .build())
                        .build();
            } else {
                clientOptions = ClusterClientOptions.builder()
                        .autoReconnect(true)
                        .protocolVersion(ProtocolVersion.RESP2)
                        .cancelCommandsOnReconnectFailure(true)
                        .pingBeforeActivateConnection(true)
                        .build();
            }

            if (sslEnabled) {
                return LettucePoolingClientConfiguration.builder()
                        .poolConfig(poolConfig)
                        .commandTimeout(Duration.ofSeconds(Integer.parseInt(redisMaxWaitTimeInSecs)))
                        .clientOptions(clientOptions)
                        .useSsl()
                        .build();
            }

            return LettucePoolingClientConfiguration.builder()
                    .poolConfig(poolConfig)
                    .commandTimeout(Duration.ofSeconds(Integer.parseInt(redisMaxWaitTimeInSecs)))
                    .clientOptions(clientOptions)
                    .build();

        } catch (Exception e) {
            log.error("Failed in creating the redis lettucePooling client configuration with maxIdleConnections:{}, minIdleConnections:{}, maxWaitTimeInSecs:{}, maxTotalConnections:{}, redisClusterMode:{}, sslEnabled:{}.",
                    redisMaxIdleConnections, redisMinIdleConnections, redisMaxWaitTimeInSecs, redisMaxTotalConnections, redisClusterMode, sslEnabled, e);
            healthMetrics.updateRedisConnectionErrorCount();
            return null;
        }

    }
}
