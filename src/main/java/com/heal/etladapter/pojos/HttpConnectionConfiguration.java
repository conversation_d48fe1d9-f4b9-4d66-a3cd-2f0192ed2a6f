package com.heal.etladapter.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.annotation.Configuration;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Configuration
public class HttpConnectionConfiguration {

    private int httpConnectionRequestTimeout;
    private int httpSocketTimeout;
    private int httpClientConnectionTimeout;
    private int maxConnections;
    private int maxConnectionsPerRoute;
    private int connectionKeepAliveTime;
    private boolean disableSslValidation;
}
