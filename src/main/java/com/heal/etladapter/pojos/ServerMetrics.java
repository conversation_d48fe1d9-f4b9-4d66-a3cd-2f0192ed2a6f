package com.heal.etladapter.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServerMetrics {
    @JsonProperty
    private String serverName;
    @JsonProperty
    private String serverId;
    @JsonProperty
    private String templateServerId;
    @JsonProperty
    private String startTime;
    @JsonProperty
    private String endTime;
    @JsonProperty
    private List<List<Device>> device;
    @JsonProperty
    private String elementsProcessed;
    @JsonProperty
    private String position;
    @JsonProperty
    private String processingTime;
}
