package com.heal.etladapter.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Relationships {

    private List<Map<String, String>> isProcessOf;
    private List<EntityIdType> runsOn;
    private List<EntityIdType> isInstanceOf;
    private List<EntityIdType> isNetworkClientOf;
    private List<Map<String, String>> runsOnProcessGroupInstance;
    private List<Map<String, String>> runsOnHost;
    private List<Map<String,String>> isServiceMethodOfService;
    private List<Map<String, String>> calls;
}
