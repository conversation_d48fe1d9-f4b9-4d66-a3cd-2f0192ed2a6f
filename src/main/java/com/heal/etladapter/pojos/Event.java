package com.heal.etladapter.pojos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Event {

    String eventId;
    long startTime;
    long endTime;
    String eventType;
    String title;
    Map<String, Object> entityId;
    List<Map<String, String>> properties;
    String status;
    String correlationId;
    List<Object> entityTags;
    List<Object> managementZones;
    boolean underMaintenance;
    boolean suppressAlert;
    boolean suppressProblem;
    boolean frequentEvent;

}
