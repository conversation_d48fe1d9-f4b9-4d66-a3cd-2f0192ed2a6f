package com.heal.etladapter.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Device {

    @JsonProperty
    private List<String> Name;
    @JsonProperty
    private List<String> IpAddress;
    @JsonProperty
    private List<String> SystemName;
    @JsonProperty
    private List<String> Type;
    @JsonProperty
    private List<String> Status;
    @JsonProperty
    private List<String> CpuUtil;
    @JsonProperty
    private List<String> MemoryUtil;
    @JsonProperty
    private List<List<Map<String, List<String>>>> port;
}
