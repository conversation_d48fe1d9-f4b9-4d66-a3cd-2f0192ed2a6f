package com.heal.etladapter.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealAlert {

	@Override
	public String toString() {
		return "HealAlert [healAlertId=" + healAlertId + ", alertTimestamp=" + alertTimestamp + ", kpiName=" + kpiName
				+ ", kpiValue=" + kpiValue + ", kpiType=" + kpiType + ", kpiId=" + kpiId + ", componentInstanceName="
				+ componentInstanceName + ", componentType=" + componentType + ", component=" + component
				+ ", componentInstanceId=" + componentInstanceId + ", componentId=" + componentId + ", severity="
				+ severity + ", applicationInstanceId=" + applicationInstanceId + ", applicationName=" + applicationName
				+ ", operationType=" + operationType + ", units=" + units + ", accountName=" + accountName + "]";
	}

	private BigInteger healAlertId;
	private Date alertTimestamp;
	private String kpiName;
	private BigDecimal kpiValue;
	private String kpiType;
	private Integer kpiId;
	private String componentInstanceName;
	private String componentType;
	private String component;
	private Integer componentInstanceId;
	private Integer componentId;
	private Short severity;
	private Integer applicationInstanceId;
	private String applicationName;
	private String operationType;
	private String units;
	private String accountName;

	public BigInteger getHealAlertId() {
		return healAlertId;
	}

	public void setHealAlertId(BigInteger healAlertId) {
		this.healAlertId = healAlertId;
	}

	public Date getAlertTimestamp() {
		return alertTimestamp;
	}

	public void setAlertTimestamp(Date alertTimestamp) {
		this.alertTimestamp = alertTimestamp;
	}

	public String getKpiName() {
		return kpiName;
	}

	public void setKpiName(String kpiName) {
		this.kpiName = kpiName;
	}

	public BigDecimal getKpiValue() {
		return kpiValue;
	}

	public void setKpiValue(BigDecimal kpiValue) {
		this.kpiValue = kpiValue;
	}

	public String getKpiType() {
		return kpiType;
	}

	public void setKpiType(String kpiType) {
		this.kpiType = kpiType;
	}

	public Integer getKpiId() {
		return kpiId;
	}

	public void setKpiId(Integer kpiId) {
		this.kpiId = kpiId;
	}

	public String getComponentInstanceName() {
		return componentInstanceName;
	}

	public void setComponentInstanceName(String componentInstanceName) {
		this.componentInstanceName = componentInstanceName;
	}

	public String getComponentType() {
		return componentType;
	}

	public void setComponentType(String componentType) {
		this.componentType = componentType;
	}

	public String getComponent() {
		return component;
	}

	public void setComponent(String component) {
		this.component = component;
	}

	public Integer getComponentInstanceId() {
		return componentInstanceId;
	}

	public void setComponentInstanceId(Integer componentInstanceId) {
		this.componentInstanceId = componentInstanceId;
	}

	public Integer getComponentId() {
		return componentId;
	}

	public void setComponentId(Integer componentId) {
		this.componentId = componentId;
	}

	public Short getSeverity() {
		return severity;
	}

	public void setSeverity(Short severity) {
		this.severity = severity;
	}

	public Integer getApplicationInstanceId() {
		return applicationInstanceId;
	}

	public void setApplicationInstanceId(Integer applicationInstanceId) {
		this.applicationInstanceId = applicationInstanceId;
	}

	public String getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public String getUnits() {
		return units;
	}

	public void setUnits(String units) {
		this.units = units;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

}
