package com.heal.etladapter.pojos;

import java.math.BigInteger;

public class ServiceNowTicket extends AbstractExternalSystemResponse {

	private String summary;
	private String description;
	private String shortDescription;
	private BigInteger healAlertId;

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getShortDescription() {
		return shortDescription;
	}

	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}

	public BigInteger getHealAlertId() {
		return healAlertId;
	}

	public void setHealAlertId(BigInteger healAlertId) {
		this.healAlertId = healAlertId;
	}

	@Override
	public String toString() {
		return "ServiceNowTicket [summary=" + summary + ", description=" + description + ", shortDescription="
				+ shortDescription + ", healAlertId=" + healAlertId + "]";
	}

}
