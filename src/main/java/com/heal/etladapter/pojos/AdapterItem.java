package com.heal.etladapter.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.stereotype.Component;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Component
public class AdapterItem<T> {

	private T sourceItem;
	private T destItem;
	protected boolean discardItem;

	@Override
	public String toString() {
		String str = "AdapterItem [discardItem=" + discardItem + ", Class Name=" + getClass() + ", SOURCE: ";
		if (sourceItem != null) {
			str += sourceItem.toString();
		} else {
			str += "NULL";
		}
		str += ", DESTINATION: ";
		if (destItem != null) {
			str += destItem.toString();
		} else {
			str += "NULL";
		}
		str += " ]";
		return str;
	}
}
