package com.heal.etladapter.pojos;

import lombok.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopologyDetails {

    private Service service;
    private Set<Instance> hostInstances;
    private Map<String, Set<Instance>> hostWiseCompInstances;
    //Used only in the case of k8s topology
    private Set<Instance> nodeInstances;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class Service {
        private String identifier;
        private String name;
        private String layer;
        @EqualsAndHashCode.Exclude
        private Set<String> desConnections;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class Instance {
        private String identifier;
        private String name;
        private String ipAddress;
        private String osVersion;
    }
}


