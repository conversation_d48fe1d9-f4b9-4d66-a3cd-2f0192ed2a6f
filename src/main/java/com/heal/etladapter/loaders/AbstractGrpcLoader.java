package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.CollatedTransactionsProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.MessagePublisherGrpc;
import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.RPCServiceProtos.ResponseCode;
import com.heal.etladapter.utility.AdapterConstants;
import io.grpc.ManagedChannel;
import io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.NettyChannelBuilder;
import io.grpc.stub.StreamObserver;
import io.netty.handler.ssl.OpenSsl;
import io.netty.handler.ssl.SslProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;

@Slf4j
@Component
public abstract class AbstractGrpcLoader<U> extends AbstractLoader<U> {
    protected ManagedChannel channel = null;
    protected boolean sslEnabled = false;
    protected String certificateFile;
    protected String grpcServer = "";
    protected Integer grpcPort = -1;
    protected Integer maxRetryAttempts;
    protected Integer retryAttempts = 0;
    protected Integer retryInterval;
    protected StreamObserver<ResponseCode> observer;

    protected ThreadLocal<MessagePublisherGrpc.MessagePublisherStub> tLocalAsyncStub = ThreadLocal.withInitial(() -> {
        MessagePublisherGrpc.MessagePublisherStub asyncStub;
        try {
            asyncStub = MessagePublisherGrpc.newStub(this.channel);
        } catch (Exception e) {
            asyncStub = null;
            log.error("Error in creating message publisher stub for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }
        return asyncStub;
    });

    @Override
    public void initialize() throws Exception {
        try {
            log.info("Initialization Parameters for grpc loader of jobId:{}, connector instance:{}, parameters:{}", jobId, connectorInstanceIdentifier, this.parameters);
            if (this.parameters != null) {
                if (this.parameters.containsKey(AdapterConstants.GRPC_SSL_ENABLED)) {
                    this.sslEnabled = Boolean.parseBoolean(this.parameters.get(AdapterConstants.GRPC_SSL_ENABLED));
                }
                if (this.parameters.containsKey(AdapterConstants.GRPC_CERT)) {
                    this.certificateFile = this.parameters.get(AdapterConstants.GRPC_CERT);
                }
                this.grpcServer = this.parameters.get(AdapterConstants.HEAL_GRPCENDPT_ADDR);
                this.grpcPort = Integer.valueOf(this.parameters.get(AdapterConstants.HEAL_GRPCENDPT_PORT));
                this.maxRetryAttempts = (this.parameters.containsKey(AdapterConstants.HEAL_RETRY_COUNT)
                        ? Integer.parseInt(this.parameters.get(AdapterConstants.HEAL_RETRY_COUNT))
                        : 5);
                this.retryInterval = 1000 * (this.parameters.containsKey(AdapterConstants.HEAL_RETRY_INTERVAL)
                        ? Integer.parseInt(this.parameters.get(AdapterConstants.HEAL_RETRY_INTERVAL))
                        : 5);
            }

            if (this.sslEnabled) {

                boolean isOpenSslAvailable = OpenSsl.isAvailable();

                log.debug("OpenSsl.isAvailable(): {}, jobId:{}, connector instance:{}", isOpenSslAvailable, jobId, connectorInstanceIdentifier);
                if (isOpenSslAvailable) {
                    log.debug("OpenSsl version: {}, jobId:{}, connector instance:{}", OpenSsl.versionString(), jobId, connectorInstanceIdentifier);
                }
                File certFile = new File(this.certificateFile);
                log.debug("Certificate file path:{}, File Exists? :{}, jobId:{}, connector instance:{}", this.certificateFile, certFile.exists(), jobId, connectorInstanceIdentifier);
                io.netty.handler.ssl.SslContext sslContext;
                if (certFile.exists()) {
                    sslContext = GrpcSslContexts.forClient().sslProvider(SslProvider.OPENSSL).trustManager(certFile)
                            .protocols("TLSv1.2").build();
                } else {
                    sslContext = GrpcSslContexts.forClient().sslProvider(SslProvider.OPENSSL).build();
                }

                channel = NettyChannelBuilder
                        .forAddress(grpcServer, grpcPort)
                        .sslContext(sslContext)
                        .build();
            } else {
                channel = NettyChannelBuilder
                        .forAddress(grpcServer, grpcPort)
                        .build();
            }

            log.info("{} Initialized for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier);
        } catch (Exception e) {
            log.error("Exception during initialization of {} with parameters {} for jobId:{}, connector instance:{}", this.getClass().getName(),
                    this.parameters, jobId, connectorInstanceIdentifier, e);
        }
    }

    protected Boolean attemptReconnection() {
        try {
            if (this.maxRetryAttempts == -1 || this.retryAttempts < this.maxRetryAttempts) {
                log.info("Attempting to re-initialize GRP connection in {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier);
                initialize();
            } else {
                log.error("Despite {} attempts, connection could not be established with endpoint {}:{} for jobId:{}, connector instance:{}", this.retryAttempts,
                        this.grpcServer, this.grpcPort, jobId, connectorInstanceIdentifier);
                return false;
            }
        } catch (Exception ex) {
            log.error("Reconnection attempt failed to endpoint {}:{} for jobId:{}, connector instance:{}", this.grpcServer, this.grpcPort, jobId, connectorInstanceIdentifier, ex);
            return false;
        }
        return true;
    }


    protected void sendData(Object item, String dataType) {
        if (item == null) {
            log.error("Adapter item or the destination item is invalid. Dropping the data.");
            return;
        }

        try {
            StreamObserver<ResponseCode> streamObserver = new StreamObserver<>() {

                @Override
                public void onNext(ResponseCode value) {
                    log.debug("Sent {} data to DR with response code : {}", dataType, value);
                }

                @Override
                public void onError(Throwable t) {
                    log.error("Error in sending {} data to DR: ", dataType, t);

                    retryAttempts++;
                    if (retryAttempts < maxRetryAttempts) {
                        try {
                            Thread.sleep(retryInterval);
                        } catch (InterruptedException e) {
                            log.error("Error while using thread sleep.", e);
                        }
                        sendData(item, dataType);
                    }
                }

                @Override
                public void onCompleted() {
                    log.debug("{} data sent successfully", dataType);
                }
            };

            if ("KPI".equalsIgnoreCase(dataType)) {
                this.tLocalAsyncStub.get().publishKPIAgentMessage((KPIAgentMessageProtos.KPIAgentMessage) item, streamObserver);
            } else if ("TRANSACTION".equalsIgnoreCase(dataType)) {
                this.tLocalAsyncStub.get().publishTransactionMessage((PSAgentMessageProtos.PSAgentMessage) item, streamObserver);
            } else if ("COLLATED_TRANSACTION".equalsIgnoreCase(dataType)) {
                this.tLocalAsyncStub.get().publishCollatedTransactionMessage((CollatedTransactionsProtos.CollatedTransactions) item, streamObserver);
            }
        } catch (Exception e) {
            log.error("Error occurred while sending kpi data. Details: {}, retryAttempts:{}", item, retryAttempts, e);
            retryAttempts++;
            if (attemptReconnection()) {
                sendData(item, dataType);
            } else {
                log.error("Unable to re-attempt connection to GRPC");
            }
        }
    }
}
