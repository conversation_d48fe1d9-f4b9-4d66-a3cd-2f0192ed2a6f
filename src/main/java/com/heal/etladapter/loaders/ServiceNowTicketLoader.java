package com.heal.etladapter.loaders;

import com.google.gson.Gson;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.HealAlertExternalSystemMapping;
import com.heal.etladapter.pojos.HttpConnectionConfiguration;
import com.heal.etladapter.pojos.ServiceNowTicket;
import com.heal.etladapter.repo.mysql.HealAlertsExternalSystemRepository;
import com.heal.etladapter.service.HttpConnection;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.Constants;
import com.heal.etladapter.utility.ServiceNowConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ServiceNowTicketLoader extends AbstractLoader<List<ServiceNowTicket>> {

    private final Map<String, String> httpHeaders = new HashMap<>();
    private final Map<String, String> httpParams = new HashMap<>();

    @Autowired
    HealAlertsExternalSystemRepository repo;
    @Autowired
    AdapterHealthMetrics healthMetrics;

    private HttpConnection httpConnection;

    @Override
    public Object load(List<ServiceNowTicket> items) {
        items.forEach(tkt -> {
            this.httpParams.put(ServiceNowConstants.SERVICENOW_REQARG_DESCRIPTION, tkt.getDescription());
            this.httpParams.put(ServiceNowConstants.SERVICENOW_REQARG_SHORTDESCRIPTION, tkt.getShortDescription());
            this.httpParams.put(ServiceNowConstants.SERVICENOW_REQARG_SUMMARY, tkt.getSummary());

            Gson gson = new Gson();
            String jsonBody = gson.toJson(this.httpParams);
            log.info("Sending Request {} with Headers {} to Endpoint {} for jobId:{}, connector instance:{}", jsonBody, this.httpHeaders,
                    this.parameters.get(ServiceNowConstants.SERVICENOW_ENDPOINT), jobId, connectorInstanceIdentifier);
            String httpResponse = httpConnection.httpPost(this.parameters.get(ServiceNowConstants.SERVICENOW_ENDPOINT), jsonBody, this.httpHeaders, this.connectorInstanceIdentifier);
            tkt.setExternalSystemResponse(httpResponse);
            tkt.setExternalSystemId(""); // TODO: Extract from response if possible, else leave as blank
            log.info("Received response {} from ServiceNow Endpoint {} for jobId:{}, connector instance:{}", httpResponse,
                    this.parameters.get(ServiceNowConstants.SERVICENOW_ENDPOINT), jobId, connectorInstanceIdentifier);
            HealAlertExternalSystemMapping mapping = new HealAlertExternalSystemMapping();
            mapping.setExternalSystem(tkt.getExternalSystem());
            mapping.setHealAlertId(tkt.getHealAlertId());
            mapping.setExternalSystemId(tkt.getExternalSystemId());
            mapping.setExternalSystemResponse(httpResponse);

            repo.save(mapping, this.jdbcTemplate);
            log.info("Alert {} loaded and mapping {} saved for jobId:{}, connector instance:{}", tkt, mapping, jobId, connectorInstanceIdentifier);

        });

        return items;
    }

    @Override
    public void initialize() throws Exception {
        HttpConnectionConfiguration httpConnectionConfiguration = HttpConnectionConfiguration.builder()
                .httpConnectionRequestTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_REQUEST_TIMEOUT, "5000")))
                .httpClientConnectionTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_TIMEOUT, "5000")))
                .httpSocketTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_SOCKET_TIMEOUT, "5000")))
                .connectionKeepAliveTime(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_KEEP_ALIVE_TIME, "30000")))
                .maxConnections(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS, "50")))
                .maxConnectionsPerRoute(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS_PER_ROUTE, "20")))
                .disableSslValidation(Boolean.parseBoolean(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_DISABLE_SSL_VALIDATION, "true")))
                .build();
        httpConnection = new HttpConnection(httpConnectionConfiguration, healthMetrics);

        if (this.parameters != null) {
            if (!this.parameters.containsKey(ServiceNowConstants.SERVICENOW_ENDPOINT)) {
                log.error("Service Now End Point not defined in configuration {} for jobId:{}, connector instance:{}", this.parameters, jobId, connectorInstanceIdentifier);
                throw new Exception("Service Now End Point not defined in configuration");
            } else {
                this.parameters.forEach((key, value) -> {
                    if (key.startsWith(AdapterConstants.ADAPTER_PARAMS_HTTP_HEADERS)) {
                        httpHeaders.put(key.replace(AdapterConstants.ADAPTER_PARAMS_HTTP_HEADERS, ""),
                                value);
                    } else if (key.startsWith(AdapterConstants.ADAPTER_PARAMS_HTTP_PARAMS)) {
                        httpParams.put(key.replace(AdapterConstants.ADAPTER_PARAMS_HTTP_PARAMS, ""),
                                value);
                    }
                });
                log.info("Initialization Params for ServiceNowTicketLoader : {}, jobId:{}, connector instance:{}", this.parameters, jobId, connectorInstanceIdentifier);
                log.info("Http Request Params for ServiceNowTicketLoader : {}, jobId:{}, connector instance:{}", this.httpParams, jobId, connectorInstanceIdentifier);
                log.info("Http Headers for ServiceNowTicketLoader : {}, jobId:{}, connector instance:{}", this.httpHeaders, jobId, connectorInstanceIdentifier);
            }
        } else {
            throw new Exception("No initialization parameters specified for Service Now Ticket Loader");
        }
    }
}
