package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.CollatedTransactionsProtos;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class HealCollatedTransactionGrpcLoader extends AbstractGrpcLoader<List<CollatedTransactionsProtos.CollatedTransactions>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;

    @Override
    public Object load(List<CollatedTransactionsProtos.CollatedTransactions> adapterItemList) {

        log.info("Number of items received in {}:{} for jobId:{}, connector instance:{}", this.className, adapterItemList.size(), jobId, connectorInstanceIdentifier);

        if (!adapterItemList.isEmpty()) {
            adapterItemList.forEach(i -> {
                log.trace("Collated transaction: {} for jobId:{}, connector instance:{}", i, jobId, connectorInstanceIdentifier);
                this.sendData(i, "COLLATED_TRANSACTION");
            });

            log.info("Collated transaction GRPC load completed for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
        } else {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_COLLATED_TRANSACTION_LOADER_ERROR, 1);
            log.error("Unable to load item since Loader items empty for jobId:{}, connector instance:{}. Details: {}", jobId, connectorInstanceIdentifier, adapterItemList);
        }

        this.retryAttempts = 0;

        log.info("No of KPIs pushed to DR: {} for jobId:{}, connector instance:{}", adapterItemList.size(), jobId, connectorInstanceIdentifier);
        healthMetrics.updateTransactionsProcessedCount(adapterItemList.size());
        return adapterItemList;
    }
}
