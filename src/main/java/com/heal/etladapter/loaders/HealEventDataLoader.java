package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.heal.configuration.pojos.ApplicationSettings;
import com.heal.configuration.pojos.DateWeekBean;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.util.DateHelper;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.config.OSConnectionConfig;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.repo.redis.RedisUtilities;
import com.heal.etladapter.utility.AdapterConstants;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.action.ActionListener;
import org.opensearch.action.bulk.BulkItemResponse;
import org.opensearch.action.bulk.BulkRequest;
import org.opensearch.action.bulk.BulkResponse;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HealEventDataLoader extends AbstractLoader<List<Anomalies>> {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private OSConnectionConfig osConnectionConfig;
    @Autowired
    private RedisUtilities redisUtilities;

    private Map<String, Integer> subTypeMap = new ConcurrentHashMap<>();

    @Value("${anomaly.queue.name:anomaly-event-action-messages}")
    private String anomalyQueueName;

    @Value("${anomaly.anomalyOutputMLESignalQueueName.name:anomaly-event-mle-signal-messages}")
    private String anomalyMleQueueName;

    @Value("${anomaly.anomalySignalQueueName.name:anomaly-event-signal-messages}")
    private String anomalySignalQueueName;

    private int osBatchSize;
    private int maxQueueSize;
    private ExecutorService osEventLoad;
    private ExecutorService mqEventLoad;
    private Connection mqConnection;
    private final Queue<IndexRequest> indexRequestListQueue = new ConcurrentLinkedQueue<>();

    @Override
    public void initialize() throws Exception {
        try {
            if (this.parameters == null || this.parameters.isEmpty()) {
                healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_CONFIGURATION_ERROR, 1);
                log.error("configuration parameters unavailable for jobId:{}, connector instance:{}. Failing the initialization {}.", jobId, connectorInstanceIdentifier, this.className);
                throw new EtlAdapterException("Configuration parameters unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            boolean isMQEnabled = this.parameters.containsKey(AdapterConstants.MQ_ENABLED) && this.parameters.get(AdapterConstants.MQ_ENABLED).equalsIgnoreCase("true");
            boolean isOSEnabled = this.parameters.containsKey(AdapterConstants.OS_ENABLED) && this.parameters.get(AdapterConstants.OS_ENABLED).equalsIgnoreCase("true");

            if (!isMQEnabled && !isOSEnabled) {
                healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_CONFIGURATION_ERROR, 1);
                log.error("Both RMQ and OpenSearch are not enabled for jobId:{}, connector instance:{}. Failing the initialization for {}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.className, this.parameters);
                throw new EtlAdapterException("Both RMQ and OpenSearch are not enabled for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            if (!this.parameters.containsKey(AdapterConstants.MQ_ENABLED) || this.parameters.get(AdapterConstants.MQ_ENABLED).equalsIgnoreCase("false")) {
                log.debug("RMQ is not enabled for {}. Real-time event data will not be pushed to RMQ for jobId:{}, connector instance:{}. Available worker_parameters: {}", this.className, jobId, connectorInstanceIdentifier, this.parameters);
            } else {
                if (!this.parameters.containsKey(AdapterConstants.MQ_CONFIG_HOST)) {
                    healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_CONFIGURATION_ERROR, 1);
                    log.error("Configuration 'mq.server.host' missing for jobId:{}, connector instance:{}. Failing the initialization for {}", jobId, connectorInstanceIdentifier, this.className);
                    throw new EtlAdapterException("Configuration 'mq.server.host' missing for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
                }

                if (!this.parameters.containsKey(AdapterConstants.MQ_CONFIG_PORT)) {
                    healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_CONFIGURATION_ERROR, 1);
                    log.error("Configuration 'mq.server.port' missing for jobId:{}, connector instance:{}. Failing the initialization for {}", jobId, connectorInstanceIdentifier, this.className);
                    throw new EtlAdapterException("Configuration 'mq.server.port' missing for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
                }

                ConnectionFactory mqConnFactory = new ConnectionFactory();
                mqConnFactory.setHost(this.parameters.get(AdapterConstants.MQ_CONFIG_HOST));
                mqConnFactory.setPort(Integer.parseInt(this.parameters.get(AdapterConstants.MQ_CONFIG_PORT)));

                if (this.parameters.containsKey(AdapterConstants.MQ_CONFIG_SSL_ENABLED) && Boolean.parseBoolean(this.parameters.get(AdapterConstants.MQ_CONFIG_SSL_ENABLED))) {
                    log.debug("RMQ SSL mode is enabled for host: {}, port: {}, jobId:{}, connector instance:{}", this.parameters.get(AdapterConstants.MQ_CONFIG_HOST), this.parameters.get(AdapterConstants.MQ_CONFIG_PORT), jobId, connectorInstanceIdentifier);
                    mqConnFactory.useSslProtocol();
                }

                mqConnFactory.setVirtualHost("/");
                int mqPoolSize = Integer.parseInt(this.parameters.getOrDefault(AdapterConstants.MQ_THREAD_POOL_SIZE, "10"));

                if (mqPoolSize <= 0) {
                    healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_CONFIGURATION_ERROR, 1);
                    log.error("'mq.thread.pool.size' configuration is invalid for jobId:{}, connector instance:{}. Failing the initialization of {}. Value should be greater than 0. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.className, this.parameters);
                    throw new EtlAdapterException("'mq.thread.pool.size' configuration is invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
                }
                try {
                    mqConnection = mqConnFactory.newConnection();
                } catch (Exception e) {
                    healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_ERROR, 1);
                    log.error("Error while creating MQ connection for jobId:{}, connector instance:{}, details: {}", jobId, connectorInstanceIdentifier, e.getMessage(), e);
                }

                mqEventLoad = Executors.newFixedThreadPool(mqPoolSize);
            }

            if (!this.parameters.containsKey(AdapterConstants.OS_ENABLED) || this.parameters.get(AdapterConstants.OS_ENABLED).equalsIgnoreCase("false")) {
                log.debug("OS is not enabled for {}. Event data will not be pushed to OS for jobId:{}, connector instance:{}. Available worker_parameters: {}", this.className, jobId, connectorInstanceIdentifier, this.parameters);
            } else {
                osBatchSize = Integer.parseInt(this.parameters.getOrDefault(AdapterConstants.OPENSEARCH_BATCH_SIZE, "100"));
                maxQueueSize = Integer.parseInt(this.parameters.getOrDefault(AdapterConstants.OPENSEARCH_BATCH_MAX_SIZE, "5000"));
                objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
                objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
                objectMapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
                int osPoolSize = Integer.parseInt(this.parameters.getOrDefault(AdapterConstants.OS_THREAD_POOL_SIZE, "10"));

                if (osPoolSize <= 0) {
                    healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_CONFIGURATION_ERROR, 1);
                    log.error("'os.thread.pool.size' configuration is invalid for jobId:{}, connector instance:{}. Failing the initialization of {}. Value should be greater than 0. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.className, this.parameters);
                    throw new EtlAdapterException("'os.thread.pool.size' configuration is invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
                }

                osEventLoad = Executors.newFixedThreadPool(osPoolSize);
            }

            subTypeMap = redisUtilities.getMstTypes().parallelStream().filter(c -> c.getTypeName().equalsIgnoreCase(
                    "AnomalySignalType")).collect(Collectors.toMap(ViewTypes::getSubTypeName, ViewTypes::getSubTypeId));

        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_INITIALIZATION_ERROR, 1);
            log.error("Error while initializing loader {}, for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            throw new EtlAdapterException(e.getMessage() + " for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

    }

    @Override
    public List<Anomalies> load(List<Anomalies> eventProtoList) {
        if (eventProtoList == null || eventProtoList.isEmpty()) {
            log.error("No anomalies identified to load for jobId:{}, connector instance:{}.", jobId, connectorInstanceIdentifier);
            return null;
        }

        healthMetrics.putInLoaderReceivedCount(this.className, eventProtoList.size());

        log.info("Number of items received in {}:{} for jobId:{}, connector instance:{}", this.className, eventProtoList.size(), jobId, connectorInstanceIdentifier);

        boolean isMQEnabled = this.parameters.containsKey(AdapterConstants.MQ_ENABLED) && this.parameters.get(AdapterConstants.MQ_ENABLED).equalsIgnoreCase("true");
        boolean isOSEnabled = this.parameters.containsKey(AdapterConstants.OS_ENABLED) && this.parameters.get(AdapterConstants.OS_ENABLED).equalsIgnoreCase("true");

        if (isMQEnabled) {
            this.mqEventLoad.submit(() -> eventProtoList.forEach(event -> {
                try {

                    String applicationName = event.getMetadata().get("appId");
                    String accountName = event.getMetadata().get("accountId");

                    //  /accounts/<account_identifier>/applications/<applicationIdentifier>/settings
                    ApplicationSettings appSetting = redisUtilities.getApplicationSettings(accountName, applicationName);

                    if (event.getMetadata().containsKey("status") && "OPEN".equalsIgnoreCase(event.getMetadata().get("status"))) {
                        AnomalyEventProtos.AnomalyEvent anomalyBuilder;
                        AnomalyEventProtos.KpiInfo kpiInfo;

                        long fromTime = Long.parseLong(event.getMetadata().get(AdapterConstants.FROM_TIME));
                        long toTime = Long.parseLong(event.getMetadata().get(AdapterConstants.TO_TIME));
                        long anomalyEndTime = Long.parseLong(event.getMetadata().getOrDefault("anomalyEndTime", "0"));
                        boolean realTimeEventGenerationRequired = (event.getAnomalyTime() >= fromTime && event.getAnomalyTime() <= toTime) ||
                                (anomalyEndTime >= fromTime && anomalyEndTime <= toTime);

                        log.debug("Status of violated event to be pushed to queue: {}. Details: eventId: {}, eventStartTime: {}, eventEndTime: {}, from:{}, to:{}, jobId:{}, connector instance:{}",
                                realTimeEventGenerationRequired, event.getAnomalyId(), event.getAnomalyTime(), anomalyEndTime, fromTime, toTime, jobId, connectorInstanceIdentifier);

                        if (event.getMetadata().containsKey("eventDataType") && event.getMetadata().get("eventDataType").equalsIgnoreCase("txn")) {
                            kpiInfo = getAnomalyKpiInfo(event, true);
                            log.debug("Anomaly Trans KPI for jobId:{}, connector instance:{}, info : {} ", jobId, connectorInstanceIdentifier, kpiInfo);
                            anomalyBuilder = getAnomalyEvent(event, kpiInfo);
                        } else {
                            kpiInfo = getAnomalyKpiInfo(event, false);
                            log.debug("Anomaly KPI for jobId:{}, connector instance:{}, info : {} ", jobId, connectorInstanceIdentifier, kpiInfo);
                            anomalyBuilder = getAnomalyEvent(event, kpiInfo);
                        }
                        if (Objects.isNull(appSetting)) {
                            pushAnomalySignalDataToRmq(anomalyBuilder);
                        } else {
                            int subTypeId = subTypeMap.getOrDefault(appSetting.getTypeName(), 413);
                            if (subTypeId == 413 || appSetting.getTypeName().equalsIgnoreCase("signal-detector")) {
                                pushAnomalySignalDataToRmq(anomalyBuilder);
                            }
                            if (subTypeId == 413 || appSetting.getTypeName().equalsIgnoreCase("event-correlation")) {
                                pushAnomalyDataToMleRmq(anomalyBuilder);
                            }
                        }
                        log.debug("Anomaly Proto to send to RMQ = {}, jobId:{}, connector instance:{}", anomalyBuilder, jobId, connectorInstanceIdentifier);
                        pushAnomalyDataToRmq(anomalyBuilder);
                    }
                } catch (Exception e) {
                    healthMetrics.putInLoaderErrors(this.className, 1);
                    log.error("Error while loading event data in RMQ for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
                }
            }));
        }
        if (isOSEnabled) {
            this.osEventLoad.submit(() -> {
                eventProtoList.forEach(event -> {
                    try {
                        List<DateWeekBean> osAuditDateIndex = DateHelper.getDatesWeeksAsString(event.getAnomalyTime(),
                                event.getAnomalyTime() + 1000, this.parameters.getOrDefault(AdapterConstants.EVENT_DATA_TIMEZONE, "Asia/Kolkata"));
                        //TODO if possible get the index name from the consul key
                        String indexName = String.format("heal_anomalies_%s_%s", event.getMetadata().get("accountId").toLowerCase(),
                                osAuditDateIndex.get(0).getIndexYearWeek());

                        addToQueue(new IndexRequest().index(indexName)
                                .id(event.getAnomalyId())
                                .source(objectMapper.writeValueAsString(event), XContentType.JSON));
                        log.info("Insert request added to OS scheduler for jobId:{}, connector instance:{}, details: {}", jobId, connectorInstanceIdentifier, event);
                    } catch (Exception e) {
                        healthMetrics.putInLoaderErrors(this.className, 1);
                        log.error("Error while writing triggered rollup job details to OpenSearch for jobId:{}, connector instance:{}, details: ", jobId, connectorInstanceIdentifier, e);
                    }
                });
                pushToOS();
            });
        }
        return eventProtoList;
    }

    private AnomalyEventProtos.AnomalyEvent getAnomalyEvent(Anomalies event, AnomalyEventProtos.KpiInfo kpiData) {
        return AnomalyEventProtos.AnomalyEvent.newBuilder()
                .setAccountId(this.parameters.get(AdapterConstants.ACCOUNT_NAME))
                .addAppId(event.getMetadata().get("appId"))
                .setKpis(kpiData)
                .setOperationType(event.getOperationType())
                .setThresholdType(event.getThresholdType())
                .setAnomalyId(event.getAnomalyId())
                .setAnomalyTriggerTimeGMT(event.getAnomalyTime())
                .setStartTimeGMT(event.getAnomalyTime())
                .setEndTimeGMT(Long.parseLong(event.getMetadata().getOrDefault("anomalyEndTime", "0")))
                .build();
    }

    private AnomalyEventProtos.KpiInfo getAnomalyKpiInfo(Anomalies event, boolean isWorkload) {
        String instanceId = isWorkload ? event.getTransactionId() : event.getInstanceId();
        return AnomalyEventProtos.KpiInfo.newBuilder()
                .setThresholdSeverity(event.getSeverity())
                .setKpiId(String.valueOf(event.getKpiId()))
                .setKpiAttribute(event.getKpiAttribute())
                .setIsWorkload(isWorkload)
                .addAllSvcId(event.getServiceId())
                .setInstanceId(instanceId)
                .putAllThresholds(event.getThresholds())
                .setValue(event.getValue())
                .putAllMetadata(removeFromAndToTimeInMeta(event.getMetadata()))
                .build();
    }

    private Map<String, String> removeFromAndToTimeInMeta(Map<String, String> metadata) {
        metadata.remove(AdapterConstants.FROM_TIME);
        metadata.remove(AdapterConstants.TO_TIME);
        return metadata;
    }

    private void addToQueue(IndexRequest indexRequest) {
        if (indexRequestListQueue.size() > maxQueueSize) {
            healthMetrics.putInLoaderDropCount(this.className.concat("OS"), 1);
            log.error("Maximum queue limit {} breached for indexRequests of jobId:{}, connector instance:{}", maxQueueSize, jobId, connectorInstanceIdentifier);
            return;
        }
        indexRequestListQueue.offer(indexRequest);
    }

    private final ActionListener<BulkResponse> listener = new ActionListener<>() {
        @Override
        public void onResponse(BulkResponse bulkResponse) {
            if (bulkResponse != null) {
                if (bulkResponse.hasFailures()) {
                    for (BulkItemResponse bulkItemResponse : bulkResponse) {
                        if (bulkItemResponse.isFailed()) {
                            BulkItemResponse.Failure failure = bulkItemResponse.getFailure();
                            healthMetrics.putInLoaderDropCount("HealEventDataLoader_OS", 1);
                            log.error("Failures during bulk indexing operations: {} of jobId:{}, connector instance:{}", failure, jobId, connectorInstanceIdentifier);
                        }
                    }
                } else {
                    healthMetrics.putInLoaderProcessedCount("HealEventDataLoader_OS", 1);
                }
            }
        }

        @Override
        public void onFailure(Exception e) {
            healthMetrics.putInLoaderDropCount("HealEventDataLoader_OS", 1);
            log.error("Exception during Bulk Indexing for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }
    };

    private void pushToOS() {
        long st = System.currentTimeMillis();
        List<IndexRequest> indexRequestList = new ArrayList<>();

        try {
            RestHighLevelClient client = osConnectionConfig.getElasticClient(this.parameters);
            if (client == null) {
                healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_ERROR, 1);
                return;
            }

            log.debug("Total number of index add request queue size:{}, OS batch:{}", indexRequestListQueue.size(), osBatchSize);

            int dataCounter = -1;

            while (indexRequestListQueue.peek() != null && ++dataCounter < osBatchSize) {
                IndexRequest index = indexRequestListQueue.poll();
                indexRequestList.add(index);
            }

            log.debug("Total add indexes size:{}, jobId:{}, connector instance:{}", indexRequestList.size(), jobId, connectorInstanceIdentifier);
            log.debug("Total number of index request queue size:{}, OS batch:{}, jobId:{}, connector instance:{}", indexRequestListQueue.size(), osBatchSize, jobId, connectorInstanceIdentifier);

            if (!indexRequestList.isEmpty()) {
                BulkRequest bulkRequest = new BulkRequest();
                indexRequestList.forEach(bulkRequest::add);
                client.bulkAsync(bulkRequest, RequestOptions.DEFAULT, listener);
            }
        } catch (Exception e) {
            healthMetrics.putInLoaderDropCount(this.className.concat("OS"), indexRequestList.size());
            log.error("Exception while scheduling data to push into OpenSearch. Size:{},jobId:{}, connector instance:{}", indexRequestList.size(), jobId, connectorInstanceIdentifier, e);
        } finally {
            log.debug("Pushed data of size {} to OpenSearch of jobId:{}, connector instance:{}, time taken {} ms.", indexRequestList.size(), jobId, connectorInstanceIdentifier, (System.currentTimeMillis() - st));
        }
    }


    private final ThreadLocal<Channel> tLocalChannel = ThreadLocal.withInitial(() -> {
        if (mqConnection == null) {
            log.error("Cannot create channel: MQ connection is null for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        try {
            Channel channel = mqConnection.createChannel();
            log.debug("Created MQ channel: {}", channel.toString());
            return channel;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_ERROR, 1);
            log.error("Error while creating channel for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
            return null;
        }
    });

    public void pushAnomalyDataToRmq(AnomalyEventProtos.AnomalyEvent item) {
        try {
            String corrId = UUID.randomUUID().toString();
            AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).correlationId(corrId).build();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            item.writeTo(os);

            Channel mqChannel = tLocalChannel.get();
            mqChannel.queueDeclare(this.anomalyQueueName, true, false, false, null);

            // If exchange key is not available, then send it directly to queue.
            if (this.parameters.containsKey(AdapterConstants.ANOMALY_MQ_CONFIG_EXCHANGE) && !this.parameters.get(AdapterConstants.ANOMALY_MQ_CONFIG_EXCHANGE).isEmpty()) {
                mqChannel.basicPublish(this.parameters.get(AdapterConstants.ANOMALY_MQ_CONFIG_EXCHANGE),
                        this.parameters.get(AdapterConstants.ANOMALY_MQ_CONFIG_ROUTINGKEY), persistent, os.toByteArray());
            } else {
                mqChannel.basicPublish("", this.anomalyQueueName, persistent, os.toByteArray());
            }
            log.debug("AnomalyEvent {} pushed to queue {}", item, this.anomalyQueueName);

            healthMetrics.putInLoaderProcessedCount(this.className.concat("_Anomalies"), 1);
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_ERROR, 1);
            log.error("IOException occurred when pushing to queue for jobId:{}, connector instance:{}, item : {}", jobId, connectorInstanceIdentifier, item, e);
        }
    }

    private void pushAnomalyDataToMleRmq(AnomalyEventProtos.AnomalyEvent item) {
        try {
            String corrId = UUID.randomUUID().toString();
            AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).correlationId(corrId).build();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            item.writeTo(os);

            Channel mqChannel = tLocalChannel.get();
            mqChannel.queueDeclare(this.anomalyMleQueueName, true, false, false, null);

            // If exchange key is not available, then send it directly to queue.
            if (this.parameters.containsKey(AdapterConstants.MQ_MLE_CONFIG_EXCHANGE) && !this.parameters.get(AdapterConstants.MQ_MLE_CONFIG_EXCHANGE).isEmpty()) {
                mqChannel.basicPublish(this.parameters.get(AdapterConstants.MQ_MLE_CONFIG_EXCHANGE),
                        this.parameters.get(AdapterConstants.MQ_MLE_CONFIG_ROUTINGKEY), persistent, os.toByteArray());
            } else {
                mqChannel.basicPublish("", this.anomalyMleQueueName, persistent, os.toByteArray());
            }

            log.debug("violatedEvent {} pushed to queue {}", item, this.anomalyMleQueueName);

            healthMetrics.putInLoaderProcessedCount(this.className.concat("_Anomalies"), 1);
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_ERROR, 1);
            log.error("IOException occurred when pushing to queue for jobId:{}, connector instance:{}, item : {}", jobId, connectorInstanceIdentifier, item, e);
        }
    }

    public void pushAnomalySignalDataToRmq(AnomalyEventProtos.AnomalyEvent item) {
        try {
            String corrId = UUID.randomUUID().toString();
            AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).correlationId(corrId).build();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            item.writeTo(os);

            Channel mqChannel = tLocalChannel.get();
            mqChannel.queueDeclare(this.anomalySignalQueueName, true, false, false, null);

            // If exchange key is not available, then send it directly to queue.
            if (this.parameters.containsKey(AdapterConstants.ANOMALY_SIGNAL_CONFIG_EXCHANGE) && !this.parameters.get(AdapterConstants.ANOMALY_SIGNAL_CONFIG_EXCHANGE).isEmpty()) {
                mqChannel.basicPublish(this.parameters.get(AdapterConstants.ANOMALY_SIGNAL_CONFIG_EXCHANGE),
                        this.parameters.get(AdapterConstants.ANOMALY_SIGNAL_CONFIG_ROUTINGKEY), persistent, os.toByteArray());
            } else {
                mqChannel.basicPublish("", this.anomalySignalQueueName, persistent, os.toByteArray());
            }
            log.debug("AnomalyEvent {} pushed to queue {}", item, this.anomalySignalQueueName);

            healthMetrics.putInLoaderProcessedCount(this.className.concat("_Anomalies"), 1);
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_EVENT_LOADER_ERROR, 1);
            log.error("IOException occurred when pushing to queue for jobId:{}, connector instance:{}, item : {}", jobId, connectorInstanceIdentifier, item, e);
        }
    }
}