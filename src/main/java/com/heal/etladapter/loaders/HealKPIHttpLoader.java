package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.enums.KpiType;
import com.heal.configuration.pojos.datareceiver.InstanceData;
import com.heal.configuration.pojos.datareceiver.KpiData;
import com.heal.configuration.pojos.datareceiver.RawKpiData;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.HttpConnectionConfiguration;
import com.heal.etladapter.service.HttpConnection;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class HealKPIHttpLoader extends AbstractLoader<List<KPIAgentMessageProtos.KPIAgentMessage>> {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AdapterHealthMetrics healthMetrics;

    private String accessToken;
    private HttpConnection httpConnection;

    @Override
    public void initialize() throws Exception {
        try {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            accessToken = KeycloakConnectionManager.getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("Unable to connect to keycloak server for access token, so transaction creation call to DR wont happen for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            }

            HttpConnectionConfiguration httpConnectionConfiguration = HttpConnectionConfiguration.builder()
                    .httpConnectionRequestTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_REQUEST_TIMEOUT, "5000")))
                    .httpClientConnectionTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_TIMEOUT, "5000")))
                    .httpSocketTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_SOCKET_TIMEOUT, "5000")))
                    .connectionKeepAliveTime(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_KEEP_ALIVE_TIME, "30000")))
                    .maxConnections(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS, "50")))
                    .maxConnectionsPerRoute(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS_PER_ROUTE, "20")))
                    .disableSslValidation(Boolean.parseBoolean(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_DISABLE_SSL_VALIDATION, "true")))
                    .build();
            httpConnection = new HttpConnection(httpConnectionConfiguration, healthMetrics);

            CloseableHttpClient client = httpConnection.getHttpConnection();
            if (client == null) {
                healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_INITIALIZATION_ERROR), 1);
                throw new EtlAdapterException("Unable to create HttpClient instance for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            if (this.parameters.get(AdapterConstants.DR_HTTP_ENDPOINT) == null || this.parameters.get(AdapterConstants.DR_HTTP_ENDPOINT).trim().isEmpty()) {
                healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_KPI_HTTP_LOADER_ERROR, 1);
                log.error("'data.receiver.kpi.endpoint' configuration parameter unavailable for jobId:{}, connector instance:{}. Available worker_parameters:{}", jobId, connectorInstanceIdentifier, this.parameters);
                throw new EtlAdapterException("'data.receiver.kpi.endpoint' configuration parameter unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_KPI_HTTP_LOADER_ERROR, 1);
            log.error("Exception while initializing loader {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            throw new EtlAdapterException("Error in initialization for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
    }

    @Override
    public Object load(List<KPIAgentMessageProtos.KPIAgentMessage> agentItemList) {
        if (agentItemList == null || agentItemList.isEmpty()) {
            log.warn("HealKPIHttpLoader not received any data from transformer for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        log.info("KPI Http loader received data from transformer for jobId:{}, connector instance:{}. items:{}", jobId, connectorInstanceIdentifier, agentItemList.size());

        List<RawKpiData> rawKpiData = getRawKpiData(agentItemList);
        String dataReceiverEndPoint = this.parameters.getOrDefault(AdapterConstants.DR_HTTP_ENDPOINT, AdapterConstants.DEFAULT_DR_HTTP_KPI_URL);
        boolean dataSent = sendDataToDataReceiver(rawKpiData, dataReceiverEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.KPI));
        if (!dataSent) {
            log.error("Error in pushing metrics to DR Endpoint: {}. Dropped {} data points for jobId:{}, connector instance:{}", dataReceiverEndPoint, rawKpiData.size(), jobId, connectorInstanceIdentifier);
            healthMetrics.updateKpisDropCount(rawKpiData.size());
            return agentItemList;
        }
        healthMetrics.updateKpisProcessedCount(rawKpiData.size());
        log.info("No of Raw KPIs pushed to DR: {} for jobId:{}, connector instance:{}", agentItemList.size(), jobId, connectorInstanceIdentifier);
        return agentItemList;
    }

    public List<RawKpiData> getRawKpiData(List<KPIAgentMessageProtos.KPIAgentMessage> agentProtos) {
        if (agentProtos == null || agentProtos.isEmpty()) {
            return Collections.emptyList();
        }
        List<RawKpiData> rawKpiDataList = new ArrayList<>();
        try {
            for (KPIAgentMessageProtos.KPIAgentMessage kpiAgentMessage : agentProtos) {
                List<InstanceData> instanceDataList = new ArrayList<>();
                for (KPIAgentMessageProtos.KPIAgentMessage.Instance instance : kpiAgentMessage.getInstancesList()) {
                    List<KpiData> kpiDataList = new ArrayList<>();
                    for (KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData : instance.getKpiDataList()) {
                        if (kpiData == null) {
                            continue;
                        }
                        Map<String, Map<String, String>> watcherKpiValue = new HashMap<>();
                        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> keyValuePairMap = kpiData.getWatcherKpiValue().getKeyValuePairMap();
                        for (Map.Entry<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> GroupKpiEntry : keyValuePairMap.entrySet()) {
                            watcherKpiValue.put(GroupKpiEntry.getKey(), GroupKpiEntry.getValue().getPairsMap());
                        }

                        log.trace("jobId:{}, connector instance:{}, KpiDataObject : {}", jobId, connectorInstanceIdentifier, kpiData);

                        KpiData kpiDataObject = KpiData.builder()
                                .kpiUid(kpiData.getKpiUid())
                                .timeInGMT(kpiData.getTimeInGMT())
                                .kpiType(KpiType.valueOf(String.valueOf(kpiData.getKpiType())))
                                .kpiName(kpiData.getKpiName())
                                .kpiGroupName(kpiData.getKpiGroupName())
                                .errorCode(kpiData.getErrorCode())
                                .isKpiGroup(!kpiData.getGroupKpi().getPairsMap().isEmpty())
                                .collectionInterval(kpiData.getCollectionInterval())
                                .val(kpiData.getVal())
                                .groupKpi(kpiData.getGroupKpi().getPairsMap())
                                .watcherKpiValue(watcherKpiValue)
                                .build();

                        log.trace("jobId:{}, connector instance:{}, Pojo KpiDataObject : {}", jobId, connectorInstanceIdentifier, kpiDataObject);

                        kpiDataList.add(kpiDataObject);
                    }

                    InstanceData instanceDataObject = InstanceData.builder()
                            .identifier(instance.getInstanceId())
                            .kpiData(kpiDataList)
                            .build();

                    instanceDataList.add(instanceDataObject);
                }

                rawKpiDataList.add(RawKpiData.builder()
                        .agentIdentifier(kpiAgentMessage.getAgentUid())
                        .instanceData(instanceDataList)
                        .build());
            }
        } catch (Exception e) {
            log.error("Error occurred while converting proto to raw kpi pojo for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }
        return rawKpiDataList;
    }

    public boolean sendDataToDataReceiver(List<RawKpiData> rawKpiData, String endPointUrl, String instanceIdentifierType) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(rawKpiData);

            Map<String, String> header = new HashMap<>();
            header.put(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken);
            header.put(AdapterConstants.CONTENT_TYPE_HEADER_NAME, AdapterConstants.CONTENT_TYPE_JSON);

            log.info("Trying to send KPI data to DR jobId:{}, connector instance:{}. requestURL :{}, payload :{}", jobId, connectorInstanceIdentifier, endPointUrl, jsonPayload);

            String tokenResponse = httpConnection.httpPost(endPointUrl, jsonPayload, header, instanceIdentifierType);

            if (tokenResponse == null) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, rawKpiData.size());
                log.error("Error in sending kpi data to DR and received null response for jobId:{}, connector instance:{}.", jobId, connectorInstanceIdentifier);
                return false;
            }

            log.info("Successfully send kpi data : {} for jobId:{}, connector instance:{}", tokenResponse, jobId, connectorInstanceIdentifier);
            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, rawKpiData.size());
            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_KPI_HTTP_LOADER_ERROR), 1);
            log.error("Exception while sending kpi data to DR!, endPointUrl : {}, jobId:{}, connector instance:{}", endPointUrl, jobId, connectorInstanceIdentifier, e);
        }
        return false;
    }
}
