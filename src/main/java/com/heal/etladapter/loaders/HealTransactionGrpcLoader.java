package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class HealTransactionGrpcLoader extends AbstractGrpcLoader<List<PSAgentMessageProtos.PSAgentMessage>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;

    @Override
    public Object load(List<PSAgentMessageProtos.PSAgentMessage> adapterItemList) {
        if (adapterItemList == null || adapterItemList.isEmpty()) {
            log.warn("HealTransactionGrpcLoader not received any data from transformer for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }
        adapterItemList.forEach(i -> {
            log.trace("Transaction: {} for jobId:{}, connector instance:{}", i, jobId, connectorInstanceIdentifier);
            this.sendData(i, "TRANSACTION");
        });
        log.info("Transaction GRPC load completed for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
        this.retryAttempts = 0;

        log.info("No of transactions pushed to DR: {} for jobId:{}, connector instance:{}", adapterItemList.size(), jobId, connectorInstanceIdentifier);
        healthMetrics.updateTransactionsProcessedCount(adapterItemList.size());
        return adapterItemList;
    }

}
