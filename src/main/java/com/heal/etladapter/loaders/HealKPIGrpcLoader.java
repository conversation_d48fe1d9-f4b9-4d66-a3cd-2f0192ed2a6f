package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class HealKPIGrpcLoader extends AbstractGrpcLoader<List<KPIAgentMessageProtos.KPIAgentMessage>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;

    @Override
    public Object load(List<KPIAgentMessageProtos.KPIAgentMessage> agentItemList) {

        log.info("Number of items received in {}:{} of jobId:{}, connector instance:{}", this.className, agentItemList.size(), jobId, connectorInstanceIdentifier);

        if (!agentItemList.isEmpty()) {
            agentItemList.forEach(i -> {
                log.trace("JobId:{}, connector instance:{} for Agent KPIs: {}", jobId, connectorInstanceIdentifier, i);
                this.sendData(i, "KPI");
            });
            log.info("KPI GRPC load completed for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
        } else {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_KPI_LOADER_ERROR, 1);
            log.error("Unable to load item since loader items are empty for jobId:{}, connector instance:{}. Details: {}", jobId, connectorInstanceIdentifier, agentItemList);
        }

        this.retryAttempts = 0;

        log.info("No of KPIs pushed to DR: {} for jobId:{}, connector instance:{}", agentItemList.size(), jobId, connectorInstanceIdentifier);

        return agentItemList;
    }
}
