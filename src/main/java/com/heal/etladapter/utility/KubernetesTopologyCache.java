package com.heal.etladapter.utility;

import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1Pod;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@Getter
public class KubernetesTopologyCache {

    @Value("${topology.queue.max.size:5000}")
    private int requestQueueMaxSize;

    private Map<String, ArrayBlockingQueue<V1Node>> nodesMap;
    private Map<String, ArrayBlockingQueue<V1Pod>> podsMap;

    @PostConstruct
    private void initializeQueues() {
        nodesMap = new ConcurrentHashMap<>();
        podsMap = new ConcurrentHashMap<>();
    }

    public void addToNodesQueue(String action, V1Node node) {
        if (nodesMap.get(action).size() > requestQueueMaxSize) {
            log.error("Maximum limit {} breached for nodes with action {}", requestQueueMaxSize, action);
            return;
        }
        ArrayBlockingQueue<V1Node> nodesQueue = nodesMap.getOrDefault(action, new ArrayBlockingQueue<>(requestQueueMaxSize));
        nodesQueue.add(node);

        nodesMap.put(action, nodesQueue);
    }

    public void addToPodsQueue(String action, V1Pod pod) {
        if (podsMap.get(action).size() > requestQueueMaxSize) {
            log.error("Maximum limit {} breached for pods with action {}", requestQueueMaxSize, action);
            return;
        }
        ArrayBlockingQueue<V1Pod> podsQueue = podsMap.getOrDefault(action, new ArrayBlockingQueue<>(requestQueueMaxSize));
        podsQueue.add(pod);

        podsMap.put(action, podsQueue);
    }
}
