package com.heal.etladapter.utility;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThreadPoolInitializer {

    public ThreadPoolTaskExecutor initializeThreadPool(int threadPoolMaxSize, int threadPoolCoreSize, int queueCapacity, String namePrefix) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(namePrefix);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        log.info("Executor service created with thread pool size: {} and name prefix: {}", threadPoolMaxSize, namePrefix);
        return executor;
    }
}
