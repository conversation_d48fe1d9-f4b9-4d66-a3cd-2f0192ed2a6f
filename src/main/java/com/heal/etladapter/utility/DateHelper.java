package com.heal.etladapter.utility;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DateHelper {
	private static final String defaultDateFormat = "yyyy-MM-dd HH:mm:ss";
	private static final String defaultDateFormatTxn = "yyyy-MM-dd HH:mm:ss.SSS";
	private static final String defaultElkDateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
	private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
	private static final Logger logger = LoggerFactory.getLogger(DateHelper.class);

	public static String date2FormatStringConverter(Date date, String format) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(format);
		return dateFormat.format(date);
	}

	public static String date2stringConverter(Date date) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(defaultDateFormat);
		dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
		return dateFormat.format(date);
	}

	public static Date string2dateConverter(String dateAsString) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(defaultDateFormat);
		try {
			return dateFormat.parse(dateAsString);
		} catch (ParseException e) {
			logger.error("Error Parsing {} into date.", dateAsString, e);
			return new Date();
		}
	}

	public static Date string2dateConverter(String dateAsString, String format) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(format);
		try {
			return dateFormat.parse(dateAsString);
		} catch (ParseException e) {
			logger.error("Error Parsing {} into date.", dateAsString, e);
			return null;
		}
	}

	public static String date2GMTString(Date date) {
		DateFormat gmtFormat = new SimpleDateFormat(defaultDateFormat);
		TimeZone gmtTime = TimeZone.getTimeZone("GMT");
		gmtFormat.setTimeZone(gmtTime);
		return gmtFormat.format(date);
	}

	public static String date2GMTString4Txn(Date date) {
		DateFormat gmtFormat = new SimpleDateFormat(defaultDateFormatTxn);
		TimeZone gmtTime = TimeZone.getTimeZone("GMT");
		gmtFormat.setTimeZone(gmtTime);
		return gmtFormat.format(date);
	}

	public static String date2GMTString4CollateTxn(Date date) {
		DateFormat gmtFormat = new SimpleDateFormat(defaultDateFormat);
		TimeZone gmtTime = TimeZone.getTimeZone("GMT");
		gmtFormat.setTimeZone(gmtTime);
		return gmtFormat.format(date);
	}

	public static Date string2ElkDateConverter(String dateAsString) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(defaultElkDateFormat);
		try {
			return dateFormat.parse(dateAsString);
		} catch (ParseException e) {
			logger.error("Error Parsing {} into date.", dateAsString, e);
			return null;
		}
	}

	public static String date2ElkString4Txn(Date date) {
		DateFormat gmtFormat = new SimpleDateFormat(defaultElkDateFormat);
		TimeZone gmtTime = TimeZone.getTimeZone("GMT");
		gmtFormat.setTimeZone(gmtTime);
		return gmtFormat.format(date);
	}

	public static String getDateInGMT(long time) throws ParseException {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN)
				.withZone(TimeZone.getTimeZone("GMT").toZoneId());
		return formatter.format(Instant.ofEpochMilli(time));
	}

	public static long getGMTToEpochTime(String time) {
		DateFormat simpleDateFormat;
		try {
			simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
			simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
			Date date = simpleDateFormat.parse(time.trim());
			return date.getTime();
		}catch (Exception e)    {
			return 0L;
		}
	}
}
