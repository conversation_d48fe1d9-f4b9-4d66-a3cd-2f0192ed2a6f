package com.heal.etladapter.utility;

public class AdapterConstants {
    public static final String GRPC_SSL_ENABLED = "grpc.ssl.enabled";
    public static final String GRPC_CERT = "grpc.certificate";
    public static final String HEAL_GRPCENDPT_ADDR = "grpc.address";
    public static final String HEAL_GRPCENDPT_PORT = "grpc.port";
    public static final String HEAL_RETRY_COUNT = "grpc.max.retry.attempts";
    public static final String HEAL_RETRY_INTERVAL = "grpc.retry.interval";

    public static final String AUTHORIZATION_HEADER_NAME = "Authorization";
    public static final String CONTENT_TYPE_HEADER_NAME = "Content-Type";
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String ACCEPT_HEADER_NAME = "Accept";
    public static final String ACCEPT_JSON = "application/json";
    public static final String ADAPTER_PARAMS_HTTP_HEADERS = "http.req.headers.";
    public static final String ADAPTER_PARAMS_HTTP_PARAMS = "http.req.params.";

    public static final String ACCOUNT_NAME = "account.name";
    public static final String LAST_UPPER_THRESHOLD = "::LastUpperThreshold";
    public static final String ADAPTER_REPOSITORY = "repository";
    public static final String ADAPTER_DEFAULT_INSTANCE = "default.instance";
    public static final String ADAPTER_DEFAULT_AGENT = "default.agent";

    public static final String DOMAIN = "domain";

    public static final String ACCOUNT = "account";

    public static final String MQ_CONFIG_HOST = "mq.server.host";
    public static final String MQ_CONFIG_PORT = "mq.server.port";
    public static final String MQ_CONFIG_EXCHANGE = "mq.exchange";
    public static final String MQ_CONFIG_EXCHANGE_TYPE = "mq-exchange-type";

    public static final String MQ_MLE_CONFIG_EXCHANGE = "mq.mle.exchange";
    public static final String MQ_MLE_CONFIG_EXCHANGE_TYPE = "mq-mle-exchange-type";
    public static final String MQ_MLE_CONFIG_ROUTINGKEY = "mq.mle.routing.key";
    public static final String MQ_CONFIG_QUEUE = "mq.queue.name";
    public static final String MQ_CONFIG_ROUTINGKEY = "mq.routing.key";
    public static final String MQ_CONFIG_SSL_ENABLED = "mq.ssl.enabled";
    public static final String MQ_ENABLED = "mq.enabled";
    public static final String OS_ENABLED = "os.enabled";
    public static final String MQ_THREAD_POOL_SIZE = "mq.thread.pool.size";
    public static final String OS_THREAD_POOL_SIZE = "os.thread.pool.size";
    public static final String EVENT_DATA_TIMEZONE = "event.data.timezone";

    public static final String ADAPTER_LISTENINGEXTRACTOR_MAXRETRY = "adapter-max-retry-attempts";
    public static final String ADAPTER_LISTENINGEXTRACTOR_RETRYINTERVAL = "adapter-max-retry-interval";

    public static final String ADAPTER_WORKER_RELOADCONFIG = "reload-configuration";
    public static final String ERROR_PROPERTY = "error.property";

    public static final String CC_AGENT_URL = "/appsone-controlcenter/v2.0/api/agents/";
    public static final String CC_TOKEN_URL = "/auth/realms/master/protocol/openid-connect/token";
    public static final String RULES = "/rules";
    public static final String REQUEST_TYPE_GET = "GET";
    public static final String REQUEST_TYPE_POST = "POST";
    public static final String AUTHORIZATION = "Authorization";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String JSON = "application/json";

    public static final String CC_AGENT_ADD_URL = "controlcenter.agent.add.url";
    public static final String CC_INSTANCE_ADD_URL = "controlcenter.instance.add.url";
    public static final String CC_SERVICE_ADD_URL = "controlcenter.service.add.url";
    public static final String CC_SERVICE_CONNECTION_URL = "controlcenter.service.connection.url";
    public static final String CC_INSTANCE_DELETE_URL = "controlcenter.instance.delete.url";
    public static final String HOST_INSTANCE = "HOST";
    public static final String HOST_INSTANCE_UPDATE = "HOST_INSTANCE_UPDATE";
    public static final String COMP_INSTANCE = "INSTANCE";
    public static final String DELETE_INSTANCE = "DELETE_INSTANCE";
    public static final String SERVICE = "SERVICE";
    public static final String SERVICE_CONNECTION = "SERVICE_CONNECTION";
    public static final String AGENT = "AGENT";
    public static final String COMP_AGENT = "ComponentAgent";
    public static final String LOG_AGENT = "LogForwarder";
    public static final String KPI = "KPI";
    public static final String DT_CHAIN_REPO = "dynatraceadapter";
    public static final String IS_KPI_MAPPING_BASED_ON_KPI_IDENTIFIER = "kpi.mapping.by.identifier";
    public static final String ADAPTER_DEFAULT_SERVICE = "default.service";
    public static final String ADAPTER_DEFAULT_APP = "default-app";
    public static final String THRESHOLD = "threshold";
    public static final String VALUE = "value";
    public static final String OPERATION_TYPE = "operation.type";
    public static final String ANOMALY_MQ_CONFIG_EXCHANGE = "anomaly-mq-exchange";
    public static final String ANOMALY_MQ_CONFIG_QUEUE = "anomalies.queue.name";
    public static final String ANOMALY_MQ_CONFIG_ROUTINGKEY = "anomaly-mq-routing-key";

    public static final String ANOMALY_SIGNAL_CONFIG_EXCHANGE = "anomaly-signal-exchange";
    public static final String ANOMALY_SIGNAL_CONFIG_QUEUE = "anomalies.signal.queue.name";
    public static final String ANOMALY_SIGNAL_CONFIG_ROUTINGKEY = "anomaly-signal-routing-key";
    public static final String FROM_TIME = "fromTime";
    public static final String TO_TIME = "toTime";

    public static final String BASE_URL= "base.url";

    public static final String AGENT_RULE_API = "agent.rule.api";

    public static final String CONNECTOR_CACHE = "connectorCache";
    public static final String INSTANCE_CACHE = "instanceCache";
    public static final String SERVICE_CACHE = "serviceCache";
    public static final String ACCOUNT_CACHE = "accountCache";
    public static final String AGENT_CACHE = "agentCache";
    public static final String REDIS_UTIL_CACHE = "redisUtilCache";

    public static final String DATA_REGEX = "data.regex";
    public static final String KC_HOST = "keycloak.host";
    public static final String KC_PORT = "keycloak.port";
    public static final String KC_USERNAME = "keycloak.username";
    public static final String KC_PASSWORD = "keycloak.password";

    public static final String OPENSEARCH_BATCH_SIZE = "opensearch.batch.size";
    public static final String OPENSEARCH_BATCH_MAX_SIZE = "opensearch.batch.queue.max.size";

    public static final String DATA_NOT_FOUND = "DATA NOT FOUND";

    public static final String HTTP_CLIENT_MAX_CONNECTIONS = "http.client.max.connections";
    public static final String HTTP_CLIENT_MAX_CONNECTIONS_PER_ROUTE = "http.client.max.connections.per.route";
    public static final String HTTP_CONNECTION_REQUEST_TIMEOUT_PROPERTY_NAME = "http.client.connection.request.timeout";
    public static final String HTTP_SOCKET_TIMEOUT_PROPERTY_NAME = "http.client.socket.timeout";
    public static final String HTTP_CONNECTION_TIMEOUT_PROPERTY_NAME = "http.client.connection.timeout";
    //Health metrics
    public static final String HEAL_KPI_TRANSFORMER_INITIALIZATION_ERROR = "HealKpiTransformerInitializationError";
    public static final String HEAL_KPI_TRANSFORMER_CONFIGURATION_ERROR = "HealKpiTransformerConfigurationError";

    public static final String HEAL_COLLATED_TRANSACTION_TRANSFORMER_INITIALIZATION_ERROR = "HealCollatedTransactionTransformerInitializationError";
    public static final String HEAL_COLLATED_TRANSACTION_TRANSFORMER_CONFIGURATION_ERROR = "HealCollatedTransactionTransformerConfigurationError";
    public static final String HEAL_COLLATED_TRANSACTION_TRANSFORMER_ERROR = "HealCollatedTransactionTransformerError";

    public static final String HEAL_TRANSACTION_TRANSFORMER_INITIALIZATION_ERROR = "HealTransactionTransformerInitializationError";
    public static final String HEAL_TRANSACTION_TRANSFORMER_CONFIGURATION_ERROR = "HealTransactionTransformerConfigurationError";
    public static final String HEAL_TRANSACTION_TRANSFORMER_ERROR = "HealTransactionTransformerError";

    public static final String HEAL_EVENT_TRANSFORMER_INITIALIZATION_ERROR = "HealEventTransformerInitializationError";
    public static final String HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR = "HealEventTransformerConfigurationError";
    public static final String HEAL_EVENT_TRANSFORMER_ERROR = "HealEventTransformerError";

    public static final String HEAL_KPI_LOADER_ERROR = "HealKpiGrpcLoaderError";
    public static final String HEAL_KPI_HTTP_LOADER_ERROR = "HealKpiHttpLoaderError";
    public static final String HEAL_TRANSACTION_LOADER_ERROR = "HealTransactionGrpcLoaderError";
    public static final String HEAL_COLLATED_TRANSACTION_LOADER_ERROR = "HealCollatedTransactionGrpcLoaderError";

    public static final String HEAL_FORENSIC_TRANSFORMER_INITIALIZATION_ERROR = "HealForensicTransformerInitializationError";
    public static final String HEAL_FORENSIC_TRANSFORMER_CONFIGURATION_ERROR = "HealForensicTransformerConfigurationError";
    public static final String HEAL_FORENSIC_TRANSFORMER_ERROR = "HealForensicTransformerError";

    public static final String HEAL_EVENT_LOADER_INITIALIZATION_ERROR = "HealEventLoaderInitializationError";
    public static final String HEAL_EVENT_LOADER_CONFIGURATION_ERROR = "HealEventLoaderConfigurationError";
    public static final String HEAL_EVENT_LOADER_ERROR = "HealEventLoaderError";

    public static final String HEAL_TOPOLOGY_LOADER_INITIALIZATION_ERROR = "HealTopologyLoaderInitializationError";
    public static final String HTTP_CONNECTION_ERROR = "HttpConnectionError";
    public static final String HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR = "HealTopologyLoaderConfigurationError";
    public static final String HEAL_TOPOLOGY_LOADER_ERROR = "HealTopologyLoaderError";

    public static final String HEAL_TOPOLOGY_HTTP_CALL_COUNT = "HealTopologyHttpCallsCount";
    public static final String HEAL_TOPOLOGY_HTTP_CALL_MIN_RESP_TIME = "HealTopologyHtpCallMinRespTime";
    public static final String HEAL_TOPOLOGY_HTTP_CALL_MAX_RESP_TIME = "HealTopologyHttpCallMaxRespTime";
    public static final String HEAL_TOPOLOGY_HTTP_CALL_FAILURE_COUNT = "HealTopologyHttpCallFailureCount";
    public static final String HEAL_KPI_HTTPS_CALL_COUNT = "HealKpiHttpsCallsCount";
    public static final String HEAL_KPI_HTTPS_CALL_MIN_RESP_TIME = "HealKpiHttpsCallMinRespTime";
    public static final String HEAL_KPI_HTTPS_CALL_MAX_RESP_TIME = "HealKpiHttpsCallMaxRespTime";
    public static final String HEAL_KPI_HTTPS_CALL_FAILURE_COUNT = "HealKpiHttpsCallFailureCount";
    public static final String DR_HTTP_ENDPOINT = "data.receiver.kpi.endpoint";
    public static final String DEFAULT_DR_HTTP_KPI_URL = "https://haproxy.appnomic:9998/raw-agents-kpi-data";
    public static final String HTTP_CALL_COUNT = "HttpCallsCount";
    public static final String HTTP_CALL_MIN_RESP_TIME = "HtpCallMinRespTime";
    public static final String HTTP_CALL_MAX_RESP_TIME = "HttpCallMaxRespTime";
    public static final String HTTP_CALL_FAILURE_COUNT = "HttpCallFailureCount";



    public static final String RESPONSE_TIME = "6";
    public static final String URI = "http://uri";

    public static final String TRANSACTION_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String IS_TIMESTAMP_UNIX = "TimeStamp_type";
    public static final String ELASTICSEARCH_DATE_FORMAT = "elasticSearch_timestamp_format";//"dd/MMM/yyyy:HH:mm:ss Z"

    public static final String USER_EXPERIENCE_NORMAL = "NORMAL";
    public static final String USER_EXPERIENCE_SLOW = "SLOW";
    public static final String USER_EXPERIENCE_VERY_SLOW = "VERY_SLOW";
    public static final String USER_EXPERIENCE_ERROR = "ERROR";
    public static final String IS_KUBERNETES_TRANSACTIONS = "is_kubernetes_transaction";
    public static final String NAME_SPACE = "nameSpace";
    public static final String POD_UID = "pod_uid";
    public static final String MATCH_PHASE = "match_phase";
    public static final String COMPONENT_SUFFIX = "component_suffix";
    public static final String ELASTIC_SEARCH_QUERY = "elastic_query";
    public static final String USER_NAME = "user_name";
    public static final String PASSWARD = "password";

    public static final String TIMESTAMP = "@timestamp";
    public static final String HTTP_RESPONSE_TIME = "http.response.time";
    public static final String LOGSCAN_SCHEMA_SERVICE_NAME = "service.name";
    public static final String HTTP_URI = "http.uri";
    public static final String HTTP_REQUEST_METHOD = "http.request.method";
    public static final String INSTANCE_NAME = "instance.name";
    public static final String HTTP_RESPONSE_STATUS_CODE = "http.response.status_code";
    public static final String LOGSCAN_SCHEMA_LOGFILETYPE = "logfile.type";
    public static final String CLIENT_IP = "client.ip";
    public static final String SERVER_IP = "server.ip";
    public static final String CLIENT_PORT = "client.port";
    public static final String SERVER_PORT = "server.port";

    public static final String LOGSCAN_INDEX_PATTERN = "index-pattern";
    public static final String LOGSCAN_FIELD_EXISTS_PATTERN = "mandatory-fields";
    public static final String LOGSCAN_FIELD_EXISTS_PATTERN_SEPARATOR = "mandatory-fields-separator";
    public static final String LOGSCAN_QUERY_SCROLLSIZE = "ls-scroll-size";
    public static final String LOGSCAN_ENDPT_ID = "logscanEndPointId";
    public static final String LOGSCAN_SLOWTXN_THRESHOLD = "logscan-slowtxn-threshold";
    public static final String LOGSCAN_ELASTICSEARCH_HOST = "elasticsearch-host";
    public static final String LOGSCAN_ELASTICSEARCH_PORT = "elasticsearch-port";
    public static final String LOGSCAN_ELASTICSEARCH_PROTOCOL = "elasticsearch-protocol";
    public static final String LOGSCAN_ELASTICSEARCH_RETRYINTERVAL = "elasticsearch-connfail-retryinterval";
    public static final String LOGSCAN_ELASTICSEARCH_MAXATTEMPTS = "elasticsearch-connfail-maxattempts";
    public static final String LOGSCAN_ELASTICSEARCH_CONNTIMEOUT = "elasticsearch-conn-timeout-in-mills";
    public static final String LOGSCAN_ELASTICSEARCH_SOCKETTIMEOUT = "elasticsearch-socket-timeout-in-mills";
    public static final String LOGSCAN_QUERY_FILTER_START = "negative-filter.";
    public static final String LOGSCAN_SUCCESS_COUNT = "success-count";
    public static final String LOGSCAN_FAILURE_COUNT = "failure-count";
    public static final String BATCH_SIZE = "batch.size";
    public static final String FIRST_2_SEGMENTS = "First 2 Segments";
    public static final String URL_NORMALIZE = "url.normalize";

    public static final String CONTAINER_IMAGE_REGEX_PATTERN = "image.regex.pattern";
    public static final String SERVICE_PLACEHOLDER_REGEX_PATTERN = "service.discovery.regex.pattern";
    public static final String ENVIRONMENT_NAME = "environment.name";
    public static final String DT_SERVICE_LAYER_TAG_MAPPING_NAME = "dt.service.layer.tag.mapping.name";
    public static final String DT_EVENT_TAG_MAPPING_NAME = "dt.event.tag.mapping.name";
    public static final String DT_COMPONENT_TAG_MAPPING_NAME = "dt.component.tag.mapping.name";

    public static final String DATASOURCE_URL = "datasource.url";
    public static final String DATASOURCE_USERNAME = "datasource.username";
    public static final String DATASOURCE_PASSWORD = "datasource.password";
    public static final String DATASOURCE_DRIVER_CLASS_NAME = "datasource.driver.class.name";
    public static final String DATASOURCE_MIN_IDLE_CONNECTIONS = "datasource.minimum.idle.connections";
    public static final String DATASOURCE_MAX_POOL_SIZE = "datasource.maximum.pool.size";
    public static final String DATASOURCE_CONNECTION_TIMEOUT = "datasource.connection.timeout";
    public static final String DATASOURCE_CONNECTION_IDLE_TIMEOUT = "datasource.connection.idle.timeout";
    public static final String DATASOURCE_MAX_LIFE_TIME = "datasource.max.life.time";

    public static final String REDIS_MAX_TOTAL_CONNECTIONS = "redis.max.total.connections";
    public static final String REDIS_MAX_IDLE_CONNECTIONS = "redis.max.idle.connections";
    public static final String REDIS_MIN_IDLE_CONNECTIONS = "redis.min.idle.connections";
    public static final String REDIS_MAX_WAIT_TIME_IN_SECS = "redis.max.wait.secs";

}
