package com.heal.etladapter.utility;

public class <PERSON>bernetesAdapterConstants {

    public static final String HEAL_CLIENT_ID = "client_id";

    public static final String HEAL_USER_NAME = "username";

    public static final String HEAL_PASSWORD = "password";

    public static final String HEAL_GRANT_TYPE = "grant_type";

    public static final String HEAL_ACCESS_TOKEN = "access_token";

    public static final String CREATE_INSTANCE_URL = "create_url";
    public static final String DELETE_INSTANCE_URL = "delete_url";
    public static final String ADAPTER_KUBERNETES_REPOSITORY = "kubernetes_repository";
    public static final String CONTROL_CENTER_ENDPOINTS = "cc_endpoints";
    public static final String AGENT_IDENTIFIER = "create_agent_url";

    public static final String POD_TIME_STAMP_Diff = "pod_time_stamp_diff";

    public static final String DELETE_NODE_INSTANCE = "DELETE_NODE";

    public static final String CLUSTER_NAME = "CLUSTER_NAME";

    public static final String CLUSTER_IDENTIFIER = "CLUSTER_IDENTIFIER";
    public static final String GRPC_ADDRESS = "grpc-address";
    public static final String GRPC_PORT = "grpc-port";
    public static final String NAME_SEPARATOR = "_";
    public static final String STAR = "*";
    public static final String COMPONENT_AGENT = "ComponentAgent";
    public static final String APPSONE_CONTROLCENTER = "appsone-controlcenter";
    public static final String DEPLOYMENT = "K8s_Deployment";
    public static final String NAMESPACE = "K8s_NameSpace";
    public static final String NODE_NAME = "K8s_NodeName";
    public static final String NODE_IP = "K8s_NodeIp";
    public static final String NODE_IDENTIFIER = "K8s_NodeIdentifier";
    public static final String K8_CLUSTER_NAME = "K8s_ClusterName";
    public static final String K8_CLUSTER_IDENTIFIER = "K8s_ClusterIdentifier";

    public static final String CONTAINER_NAME_ATTRIBUTE = "ContainerName";

    public static final String CONTAINER_NAME_ATTRIBUTE_DEFAULT = "COMPONENT_NAME";
    public static final String DEPLOYMENT_LABEL_ATTRIBUTE = "deployment_label_pattern";

    public static final String MASTER_URL_ID = "K8s_MASTER_URL_ID";

    public static final String KUBE_CONFIG_ID = "K8s_KUBE_CONFIG_ID";

    public static final String AUTH_HEADER = "Authorization";

    public static final String CONTENT_TYPE_HEADER = "Content-Type";

    public static final String CONTENT_TYPE_VALUE = "application/json";

    public static final String METHOD_NAME = "DELETE";

    public static final int STATUS_CODE = 200;

    public static final String ADDED_EVENT = "ADDED";

    public static final String DELETED_EVENT = "DELETED";

    public static final String RESPONSE_STATUS_SUCCESS = "SUCCESS";
    public static final String RESPONSE_STATUS_FAILURE = "FAILURE";

    public static final String HOST_ADDRESS_ATTRIBUTE = "HostAddress";
    public static final String NODE_INTERNAL_IP = "InternalIP";
    public static final String APPSONE_API = "appsone-controlcenter";
    public static final String LOG_FORWARDER_AGENT = "LogForwarder";

    public static final String CACHED_PODS_SCHEDULED_INTERVAL = "cached_Pods_update_interval";
    public static final String CACHED_SERVICES_SCHEDULED_INTERVAL = "cached_services_update_interval";

    public static final String CACHED_PODS_SCHEDULED_INTERVAL_DEFAULT = "300000"; // 5 mins
    public static final String CACHED_SERVICES_SCHEDULED_INTERVAL_DEFAULT = "300000"; // 5 mins

    public static final String INSTANCE_NAME_CHARACTER_LIMIT = "instance_name_char_limit";

    public static final String HOST_NAME_DEFAULT = "${podName}_${nameSpace}";

    public static final String HOST_NAME = "host_name";

    public static final String COMP_INS_NAME_DEFAULT = "${containerName}_${podName}_${nameSpace}";

    public static final String COMP_INS_NAME = "comp_ins_name";

    public static final String INSTANCE_REMOVAL_STATUS = "&instanceRemoval=true";

    public static final String INSTANCE_REMOVAL_MODE = "instance_removal_mode";

    public static final String INSTANCE_DELETE_HARD = "hard";
    public static final String DISABLE_VERIFY_HOSTNAME = "disable_verify_hostname";
    public static final String HEAL_SERVICE_DETAILS_IDS = "heal_service_details_ids";

    public static final String READ_TIMEOUT = "k8s.http.client.read.timeout";
    public static final String WRITE_TIMEOUT = "k8s.http.client.write.timeout";
    public static final String CONNECT_TIMEOUT = "k8s.http.client.connect.timeout";
    public static final String CALL_TIMEOUT = "k8s.http.client.call.timeout";
}
