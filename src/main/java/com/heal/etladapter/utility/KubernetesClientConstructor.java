package com.heal.etladapter.utility;

import com.heal.etladapter.beans.KubernetesConfig;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.Config;
import io.kubernetes.client.util.KubeConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.StringReader;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Component
public class KubernetesClientConstructor {

    public ApiClient getClientUsingKubeConfig(KubernetesConfig kubernetesConfig, String chainIdentifier) {
        ApiClient client = null;
        try {
            log.info("KubernetesNodeSync Trying to create client using config file");
            try {
                client = Config.fromConfig(
                        KubeConfig.loadKubeConfig(
                                new StringReader(new String(java.util.Base64.getDecoder().decode(kubernetesConfig.getFileContent().getBytes(UTF_8))))
                        ));
            } catch (Exception e) {
                log.error("KubernetesNodeSync Failed to create kubernetes client using config file : ", e);
            }
            if (client == null) {
                log.info("KubernetesNodeSync kube config url failed to create client");

                log.info("KubernetesNodeSync Trying to create client using the master url and token. ChainIdentifier: {}, master url: {}", chainIdentifier, kubernetesConfig);
                try {
                    client = Config.fromToken(kubernetesConfig.getMaster_url(),
                            kubernetesConfig.getToken(),
                            kubernetesConfig.isSslEnabled());
                } catch (Throwable e) {
                    log.error("KubernetesNodeSync Failed to create kubernetes client using master URL. ChainIdentifier: {}, master url: {}", chainIdentifier, kubernetesConfig, e);
                }
                if (client == null) {
                    log.info("KubernetesNodeSync master url failed to create client using master URL. ChainIdentifier: {}, master url: {}", chainIdentifier, kubernetesConfig);
                } else {
                    log.info("KubernetesNodeSync master url successfully create client using master URL. ChainIdentifier: {}, master url: {}", chainIdentifier, kubernetesConfig);
                }
            } else {
                log.info("KubernetesNodeSync kube config successfully create client");
            }
        } catch (Exception e) {
            log.error("Exception in getClientUsingKubeConfig : ", e);
        }
        return client;
    }
}
