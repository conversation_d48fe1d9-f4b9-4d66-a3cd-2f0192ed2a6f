package com.heal.etladapter.utility;

import com.heal.etladapter.beans.cc.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CCPayloadUtility {
    /**
     * create host instance payload
     *
     * @param hostId
     * @param name
     * @param agentIdentifiers
     * @param serviceIdentifier
     * @param compName
     * @param compVersion
     * @param ipAddress
     * @return
     */
    public HealInstancePayload getHostInstancePayload(String ipAddress, String hostId, String name, List<String> agentIdentifiers, Set<String> serviceIdentifier, String compName,
                                                             String compVersion, String environment) {
        return HealInstancePayload.builder()
                .identifier(hostId)
                .name(name)
                .serviceIdentifiers(serviceIdentifier)
                .agentIdentifiers(agentIdentifiers)
                .componentName(compName)
                .componentVersion(compVersion)
                .attributes(createBasicAttribute(ipAddress))
                .environment(environment)
                .build();
    }

    /**
     * create heal component instance payload
     *
     * @param ipAddress
     * @param instanceIdentifier
     * @param agentIdentifiers
     * @param serviceIdentifier
     * @param compName
     * @param compVersion
     * @param name
     * @return
     */
    public HealInstancePayload getCompInstancePayload(String ipAddress, String instanceIdentifier, String name, List<String> agentIdentifiers,
                                                             Set<String> serviceIdentifier, String compName,
                                                             String compVersion, String environment) {
        return HealInstancePayload.builder()
                .identifier(instanceIdentifier)
                .name(name)
                .agentIdentifiers(agentIdentifiers)
                .serviceIdentifiers(serviceIdentifier)
                .componentName(compName)
                .componentVersion(compVersion)
                .attributes(createBasicAttribute(ipAddress))
                .environment(environment)
                .build();
    }

    /**
     * create heal agent payload
     *
     * @param agentName
     * @param serviceName
     * @param ipAddress
     * @return
     */
    public HealAgentPayload getHealAgentPayload(String agentName, String serviceName, String ipAddress,
                                                       String subType, String grpcHost, String grpcPort) {
        ArrayList<String> dataSources = new ArrayList<>();
        DataCommunication dataCommunication = DataCommunication.builder()
                .host(grpcHost)
                .port(grpcPort)
                .build();
        return HealAgentPayload.builder()
                .name(agentName)
                .uniqueToken(agentName)
                .hostAddress(ipAddress)
                .subType(subType)
                .serviceName(serviceName)
                .addedDataSources(dataSources)
                .agentMappingDetails(AgentMappingDetails.builder().dataCommunication(dataCommunication).build())
                .build();
    }

    /**
     * get heal service payload
     *
     * @param serviceId
     * @param serviceName
     * @param layer
     * @param appIdentifier
     * @param timeZone
     * @return
     */
    public HealServicePayload getHealServicePayload(String serviceId, String serviceName, String layer, String appIdentifier,
                                                           String timeZone) {
        Set<String> appIdentifiers = new HashSet<>();
        appIdentifiers.add(appIdentifier);
        return HealServicePayload.builder()
                .identifier(serviceId)
                .name(serviceName)
                .appIdentifiers(appIdentifiers)
                .layer(layer)
                .timezone(timeZone)
                .build();
    }

    /**
     * create the basic attribute
     *
     * @param hostAddress
     * @return list of attributes
     */
    private List<Attributes> createBasicAttribute(String hostAddress) {
        List<Attributes> attributesList = new ArrayList<>();
        Attributes hostAddressAttribute = Attributes.builder()
                .name("HostAddress")
                .value(hostAddress).build();
        attributesList.add(hostAddressAttribute);
        return attributesList;
    }

    public HealInstanceUpdatePayload getHealInstanceUpdatePayload(int instanceId, String instanceIdentifier, List<ApplicationPojo> applications) {
        return HealInstanceUpdatePayload.builder()
                .instanceId(instanceId)
                .instanceIdentifier(instanceIdentifier)
                .application(applications)
                .build();
    }

    public HealInstanceDeletePayload getHealInstanceDeletePayload(String instanceId, String instanceType) {
        return HealInstanceDeletePayload.builder()
                .instanceId(instanceId)
                .instanceType(instanceType)
                .build();
    }

    public HealServiceConnectionPayload getHealServiceConnectionPayload(String sourceServiceIdentifier, String destinationServiceIdentifier) {
        return HealServiceConnectionPayload.builder()
                .sourceServiceIdentifier(sourceServiceIdentifier)
                .destinationServiceIdentifier(destinationServiceIdentifier)
                .build();
    }
}
