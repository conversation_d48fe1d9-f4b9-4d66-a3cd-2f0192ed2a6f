package com.heal.etladapter.service;

import com.appnomic.appsone.transaction.mapper.*;
import com.appnomic.appsone.transaction.mapper.common.Constants;
import com.appnomic.jim.bean.PairDataBean;
import com.appnomic.jim.bean.RegexTypeDetailBean;
import com.appnomic.jim.bean.RequestTypeDetailBean;
import com.appnomic.jim.bean.RuleBean;
import com.heal.configuration.pojos.Rule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class TransactionMapperService {
    public MappedTxn getMappedRuleFromTransactionMapper(List<Rule> rules, String serviceIdentifier, String txnIdentifier) {
        List<RuleBean> ruleBeans = new ArrayList<>();
        log.info("Get mapped rule from txn mapper! txn : {}, service : {}", txnIdentifier, serviceIdentifier);
        try {
            ruleBeans = rules.stream().map(rule -> {

                RuleBean ruleBean = new RuleBean();
                ruleBean.setId(rule.getId());
                ruleBean.setName(rule.getName());
                ruleBean.setEnabled(rule.isMonitoringEnabled());
                ruleBean.setDefaultRule(rule.getIsDefault() == 1);
                ruleBean.setOrder(rule.getOrder());
                ruleBean.setRuleTypeId(rule.getRuleTypeId());
                ruleBean.setRuleFor(rule.getRuleType());
                //ruleBean.setTags(rule.getTags());
                ruleBean.setServiceIdentifier(serviceIdentifier);

                if (rule.getRegexTypeDetails() != null) {
                    RegexTypeDetailBean rtdb = new RegexTypeDetailBean();
                    rtdb.setId(rule.getRegexTypeDetails().getId());
                    rtdb.setInitialPattern(rule.getRegexTypeDetails().getInitialPattern());
                    rtdb.setEndPattern(rule.getRegexTypeDetails().getEndPattern());
                    rtdb.setLength(rule.getRegexTypeDetails().getLength());
                    ruleBean.setRegexTypeDetails(rtdb);
                }

                if (rule.getRequestTypeDetails() != null) {
                    RequestTypeDetailBean rtdb = new RequestTypeDetailBean();
                    rtdb.setId(rule.getId());
                    rtdb.setFirstSegment(String.valueOf(rule.getRequestTypeDetails().getFirstUriSegments()));
                    rtdb.setLastSegment(String.valueOf(rule.getRequestTypeDetails().getLastUriSegments()));
                    rtdb.setCompleteURI(rule.getRequestTypeDetails().isCompleteURI());
                    rtdb.setPayloadTypeId(rule.getRequestTypeDetails().getPayloadTypeId());
                    rtdb.setPayloadTypeName(rule.getRequestTypeDetails().getPayloadTypeName());

                    if (rule.getRequestTypeDetails().getPairData() != null) {
                        List<PairDataBean> pairDataBeans = rule.getRequestTypeDetails().getPairData().stream()
                                .map(pair -> {
                                    PairDataBean pairDataBean = new PairDataBean();
                                    pairDataBean.setId(pair.getId());
                                    pairDataBean.setPairTypeId(pair.getPairTypeId());
                                    pairDataBean.setPairTypeName(pair.getPairTypeName());
                                    pairDataBean.setParamKey(pair.getParamKey());
                                    pairDataBean.setParamValue(pair.getParamValue());
                                    return pairDataBean;
                                }).collect(Collectors.toList());
                        rtdb.setPairData(pairDataBeans);
                    }
                    ruleBean.setRequestTypeDetails(rtdb);
                }
                return ruleBean;

            }).collect(Collectors.toList());
            log.trace("All rule bean objects : {}", ruleBeans);
        } catch (Exception e) {
            log.error("Error in converting rules beans to rule beans", e);
        }

        Map<Integer, String> rulesTxnMapperMap = ruleBeans.stream().collect(Collectors.toMap(RuleBean::getId, RuleBean::getRuleFor, (r, v) -> r));
        log.debug("Rules txn mapper : {} ", rulesTxnMapperMap);

        if (ruleBeans.isEmpty()) {
            log.error("Couldn't get mapped txn! RuleBean is empty");
            return null;
        }

        TxnMapperConfigurator txnMapperConfigurator = new TxnMapperConfigurator();
        txnMapperConfigurator.load(ruleBeans);

        Optional<MappedTxn> mappedTxnOpt = rules.stream().map(rule -> {
                    String ruleFor = rulesTxnMapperMap.get(rule.getId());
                    TxnMapper txnMapper = null;
                    if (ruleFor.equals(Constants.AGENT_RULES_TYPE_REQUEST_DATA)) {
                        txnMapper = TxnMapperFactory.getMapper(TxnType.REQUESTDATA, txnMapperConfigurator);
                    } else if (ruleFor.equals(Constants.AGENT_RULES_TYPE_EJB_DATA)) {
                        txnMapper = TxnMapperFactory.getMapper(TxnType.EJB, txnMapperConfigurator);
                    } else {
                        log.error("Couldn't find txn mapper for rules.");
                    }
                    return txnMapper;
                })
                .filter(Objects::nonNull)
                .map(txnMapper -> {
                    RawTxn rawTxn = RawTxn.builder().uri(txnIdentifier).build();
                    MappedTxn mappedTxn = txnMapper.map(rawTxn);
                    log.info("Matched txn mapper : {}", mappedTxn);
                    return mappedTxn;
                }).findFirst();
        return mappedTxnOpt.orElse(null);
    }
}
