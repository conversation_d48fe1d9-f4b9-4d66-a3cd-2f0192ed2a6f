package com.heal.etladapter.service;

import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.HttpConnectionConfiguration;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
@Scope("prototype")
public class HttpConnection {

    AdapterHealthMetrics healthMetrics;

    private CloseableHttpClient client;
    private final HttpConnectionConfiguration httpConnectionConfiguration;

    public HttpConnection(HttpConnectionConfiguration httpConnectionConfiguration, AdapterHealthMetrics healthMetrics) {
        this.httpConnectionConfiguration = httpConnectionConfiguration;
        this.healthMetrics = healthMetrics;
    }

    public CloseableHttpClient getHttpConnection() throws EtlAdapterException {
        if (client != null) {
            return client;
        }
        try {
            PoolingHttpClientConnectionManager connectionManager = createConnectionManager();

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(httpConnectionConfiguration.getHttpConnectionRequestTimeout())
                    .setSocketTimeout(httpConnectionConfiguration.getHttpSocketTimeout())
                    .setConnectTimeout(httpConnectionConfiguration.getHttpClientConnectionTimeout())
                    .build();

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .setKeepAliveStrategy((response, context) -> httpConnectionConfiguration.getConnectionKeepAliveTime())
                    .build();

            if (httpClient == null) {
                log.error("Unable to create HTTP client. Configuration details: connectionManager: {}, requestConfig: {}", connectionManager, requestConfig);
                throw new EtlAdapterException("Unable to create HTTP client");
            }
            client = httpClient;
            log.info("Http client created with requestConfig: {}, maxConnections: {}, maxConnectionsPerRoute: {}", requestConfig, connectionManager.getMaxTotal(), connectionManager.getDefaultMaxPerRoute());
        } catch (Exception e) {
            log.error("Failed to create Http client with httpConnection configuration: {}", httpConnectionConfiguration, e);
            throw new EtlAdapterException(e.getMessage());
        }
        return client;
    }

    private PoolingHttpClientConnectionManager createConnectionManager() throws EtlAdapterException {
        try {
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

            if (httpConnectionConfiguration.isDisableSslValidation()) {
                // 1. Build "trust all" SSLContext
                SSLContextBuilder sslContextBuilder = new SSLContextBuilder().loadTrustMaterial(null, (chain, authType) -> true);

                // 2. Create a SocketFactory with "trust all" context + NoopHostnameVerifier
                SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContextBuilder.build(), NoopHostnameVerifier.INSTANCE);

                log.warn("SSL validation is disabled. This is insecure and should not be used in production!");

                // 3. Register the custom SSL socket factory
                Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", PlainConnectionSocketFactory.getSocketFactory())
                        .register("https", sslSocketFactory)
                        .build();

                // 4. Create the PoolingHttpClientConnectionManager with this registry
                connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            }

            // 5. Set your connection limits
            connectionManager.setMaxTotal(httpConnectionConfiguration.getMaxConnections());
            connectionManager.setDefaultMaxPerRoute(httpConnectionConfiguration.getMaxConnectionsPerRoute());

            log.info("Connection manager, max connections:{}, per route:{}", connectionManager.getMaxTotal(), connectionManager.getDefaultMaxPerRoute());

            return connectionManager;
        } catch (Exception e) {
            log.error("Failed to create custom connection manager", e);
            throw new EtlAdapterException("Failed to create custom PoolingHttpClientConnectionManager");
        }
    }

    public String httpGet(String url, Map<String, String> headers) {
        long time = System.currentTimeMillis();
        try {
            HttpGet request = new HttpGet(url);
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(request::addHeader);
            }

            try (CloseableHttpResponse resp = getHttpConnection().execute(request)) {
                long httpCallRespTime = System.currentTimeMillis() - time;
                log.trace("GET request: {}, Response: {}, response entity: {}", request, resp, resp.getEntity());

                if (resp.getStatusLine().getStatusCode() < 200 || resp.getStatusLine().getStatusCode() > 299) {
                    log.error("Exception occurred while executing the GET call end point {} - {} ", request, resp);
                    healthMetrics.putInExtractorHttpCallDetails(AdapterConstants.HTTP_CALL_FAILURE_COUNT, 1);
                    throw new EtlAdapterException("Bad Request. Check the request params/ user credentials");
                }
                String response = EntityUtils.toString(resp.getEntity());

                healthMetrics.putInExtractorHttpCallDetails(AdapterConstants.HTTP_CALL_COUNT, 1);
                healthMetrics.putInExtractorHttpCallDetails(AdapterConstants.HTTP_CALL_MAX_RESP_TIME,
                        Math.max(healthMetrics.getExtractorHttpCallDetails().getOrDefault(AdapterConstants.HTTP_CALL_MAX_RESP_TIME, Long.MIN_VALUE), httpCallRespTime));
                healthMetrics.putInExtractorHttpCallDetails(AdapterConstants.HTTP_CALL_MIN_RESP_TIME,
                        Math.min(healthMetrics.getExtractorHttpCallDetails().getOrDefault(AdapterConstants.HTTP_CALL_MIN_RESP_TIME, Long.MAX_VALUE), httpCallRespTime));

                log.info("GET request {}, response code: {}, time taken: {}", url, resp.getStatusLine().getStatusCode(), (System.currentTimeMillis() - time));
                log.trace("GET Request - res code: '{}' for  url: '{}' -  content: '{}'", resp.getStatusLine().getStatusCode(), url, response);

                return (resp.getStatusLine().getStatusCode() >= 200 && resp.getStatusLine().getStatusCode() <= 299) ? response : null;
            }
        } catch (Exception e) {
            log.error("Failed to send http get request. URL: {}, headers: {}", url, headers, e);
            return null;
        }
    }

    public String httpPost(String url, String jsonBody, Map<String, String> headers, String connectorInstanceIdentifier) {
        long time = System.currentTimeMillis();
        try {
            URIBuilder builder = new URIBuilder(url);
            final HttpPost httpPost = new HttpPost(builder.build());
            if (jsonBody != null) {
                httpPost.setEntity(new StringEntity(jsonBody));
            }

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpPost::addHeader);
            }

            try (CloseableHttpResponse resp = getHttpConnection().execute(httpPost)) {
                long httpCallRespTime = System.currentTimeMillis() - time;
                String response = EntityUtils.toString(resp.getEntity());
                log.trace("POST request: {}, Response: {}, response entity: {}", httpPost, resp, resp.getEntity());

                if (resp.getStatusLine().getStatusCode() < 200 || resp.getStatusLine().getStatusCode() > 299) {
                    healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_FAILURE_COUNT), 1);
                    log.error("Exception occurred while executing the POST end point {} - {} ", url, resp);
                    throw new Exception("Bad Request. Check the request params/ user credentials");
                }

                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_COUNT), 1);
                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MAX_RESP_TIME),
                        Math.max(healthMetrics.getExtractorHttpCallDetails()
                                .getOrDefault(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MAX_RESP_TIME), Long.MIN_VALUE), httpCallRespTime));
                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MIN_RESP_TIME),
                        Math.min(healthMetrics.getExtractorHttpCallDetails()
                                .getOrDefault(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MIN_RESP_TIME), Long.MAX_VALUE), httpCallRespTime));

                log.info("POST Request - res code: '{}' for  url: '{}' -  content: '{}'", resp.getStatusLine().getStatusCode(), builder.build().toString(), response);

                return (resp.getStatusLine().getStatusCode() >= 200 && resp.getStatusLine().getStatusCode() <= 299) ? response : null;
            }
        } catch (Exception e) {
            log.error("Failed to send http post request. URL: {}, headers: {}", url, headers, e);
            return null;
        }
    }

    public String httpDelete(String url, Map<String, String> headers, String connectorInstanceIdentifier) {
        long time = System.currentTimeMillis();
        try {
            URIBuilder builder = new URIBuilder(url);
            final HttpDelete httpPost = new HttpDelete(url);

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpPost::addHeader);
            }

            try (CloseableHttpResponse resp = getHttpConnection().execute(httpPost)) {
                long httpCallRespTime = System.currentTimeMillis() - time;
                String response = EntityUtils.toString(resp.getEntity());
                log.trace("DELETE request: {}, Response: {}, response entity: {}", httpPost, resp, resp.getEntity());

                if (resp.getStatusLine().getStatusCode() < 200 || resp.getStatusLine().getStatusCode() > 299) {
                    healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_FAILURE_COUNT), 1);
                    log.error("Exception occurred while executing the DELETE end point {} - {} ", url, resp);
                    throw new Exception("Bad Request. Check the request params/ user credentials");
                }

                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_COUNT), 1);
                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MAX_RESP_TIME),
                        Math.max(healthMetrics.getExtractorHttpCallDetails()
                                .getOrDefault(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MAX_RESP_TIME), Long.MIN_VALUE), httpCallRespTime));
                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MIN_RESP_TIME),
                        Math.min(healthMetrics.getExtractorHttpCallDetails()
                                .getOrDefault(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MIN_RESP_TIME), Long.MAX_VALUE), httpCallRespTime));

                log.info("DELETE Request - res code: '{}' for  url: '{}' -  content: '{}'", resp.getStatusLine().getStatusCode(), builder.build().toString(), response);

                return (resp.getStatusLine().getStatusCode() >= 200 && resp.getStatusLine().getStatusCode() <= 299) ? response : null;
            }
        } catch (Exception e) {
            log.error("Failed to send http delete request. URL: {}, headers: {}", url, headers, e);
            return null;
        }
    }

    public String httpPut(String url, String jsonBody, Map<String, String> headers, String connectorInstanceIdentifier) {
        long time = System.currentTimeMillis();

        try {
            URIBuilder builder = new URIBuilder(url);
            final HttpPut httpPut = new HttpPut(builder.build());
            if (jsonBody != null) {
                httpPut.setEntity(new StringEntity(jsonBody));
            }

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpPut::addHeader);
            }

            try (CloseableHttpResponse resp = client.execute(httpPut)) {
                long httpCallRespTime = System.currentTimeMillis() - time;

                String response = EntityUtils.toString(resp.getEntity());

                if (resp.getStatusLine().getStatusCode() < 200 || resp.getStatusLine().getStatusCode() > 299) {
                    healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_FAILURE_COUNT), 1);
                    log.error("Exception occurred while executing the CC end point {} - {} ", url, response);
                    throw new Exception("Bad Request. Check the request params/ user credentials");
                }

                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_COUNT), 1);
                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MAX_RESP_TIME),
                        Math.max(healthMetrics.getExtractorHttpCallDetails()
                                .getOrDefault(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MAX_RESP_TIME), Long.MIN_VALUE), httpCallRespTime));
                healthMetrics.putInExtractorHttpCallDetails(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MIN_RESP_TIME),
                        Math.min(healthMetrics.getExtractorHttpCallDetails()
                                .getOrDefault(connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HTTP_CALL_MIN_RESP_TIME), Long.MAX_VALUE), httpCallRespTime));

                log.info("PUT Request - res code: '{}' for  url: '{}' -  content: '{}'", resp.getStatusLine().getStatusCode(), builder.build().toString(), response);

                return (resp.getStatusLine().getStatusCode() >= 200 && resp.getStatusLine().getStatusCode() <= 299) ? response : null;
            }
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR, 1);
            log.error("Failed to send http put request. URL: {}, headers: {}", url, headers, e);
            return null;
        }
    }

}
