package com.heal.etladapter.service;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.heal.etladapter.aop.LogExecutionAnnotation;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.config.RabbitMqConfig;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RabbitListener(queues = "${forensic.command.queue.name}")
public class ForensicCommandRequestReceiver {

    @Autowired
    protected RabbitMqConfig rabbitMqConfig;

    @Autowired
    protected ForensicDataCollectionHandler forensicDataCollectionHandler;

    @Autowired
    protected AdapterHealthMetrics healthMetrics;

    /**
     * Receives and processes CommandRequest messages from the forensic command queue.
     *
     * This method handles incoming forensic command requests by:
     * 1. Parsing the CommandRequest protobuf message
     * 2. Validating the command structure and content
     * 3. Filtering supported command types
     * 4. Delegating to ForensicDataCollectionHandler for processing
     * 5. Updating health metrics for monitoring
     *
     * @param commandRequestBytes byte array containing the CommandRequest protobuf message
     */
    @RabbitHandler
    @LogExecutionAnnotation
    public void receive(byte[] commandRequestBytes) {
        long startTime = System.currentTimeMillis();
        CommandRequestProtos.CommandRequest commandRequest = null;
        
        try {
            // Update received message count
            healthMetrics.updateRmqMessagesReceivedCount(1);
            
            // Parse CommandRequest from byte array
            commandRequest = CommandRequestProtos.CommandRequest.parseFrom(commandRequestBytes);
            
            log.info("Forensic command request received: Agent={}, Commands={}, TriggerSource={}", 
                    commandRequest.getAgentIdentifier(), 
                    commandRequest.getCommandsCount(),
                    commandRequest.getTriggerSource());
            
            log.trace("Full command request details: {}", commandRequest);

            // Validate command request
            if (!isValidCommandRequest(commandRequest)) {
                log.warn("Invalid command request received, dropping message: {}", commandRequest);
                healthMetrics.updateRmqMessagesDropCount();
                return;
            }

            // Filter and process supported commands
            CommandRequestProtos.CommandRequest filteredRequest = filterSupportedCommands(commandRequest);
            if (filteredRequest.getCommandsCount() == 0) {
                log.warn("No supported forensic commands found in request, dropping message: {}", commandRequest);
                healthMetrics.updateRmqMessagesDropCount();
                return;
            }

            // Update metrics for supported commands
            healthMetrics.putInExtractorReceivedCount("ForensicCommandRequestReceiver", filteredRequest.getCommandsCount());

            // Process the forensic command request
            forensicDataCollectionHandler.processForensicCommands(filteredRequest);

            log.info("Successfully processed forensic command request with {} commands in {}ms", 
                    filteredRequest.getCommandsCount(), 
                    System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("Exception in receiving forensic command request from queue: {}", 
                    getQueueName(), e);
            healthMetrics.updateRmqMessagesDropCount();
            healthMetrics.putInExtractorErrors(AdapterConstants.FORENSIC_EXTRACTOR_ERROR, 1);
            
            // Log command details for debugging if available
            if (commandRequest != null) {
                log.error("Failed command request details: Agent={}, Commands={}, Error={}", 
                        commandRequest.getAgentIdentifier(), 
                        commandRequest.getCommandsCount(), 
                        e.getMessage());
            }
        }
    }

    /**
     * Validates the structure and content of a CommandRequest.
     *
     * Performs comprehensive validation including:
     * - Required fields presence
     * - Agent identifier validation
     * - Command list validation
     * - Timestamp validation
     *
     * @param commandRequest the CommandRequest to validate
     * @return true if the command request is valid, false otherwise
     */
    private boolean isValidCommandRequest(CommandRequestProtos.CommandRequest commandRequest) {
        if (commandRequest == null) {
            log.warn("Command request is null");
            return false;
        }

        // Validate agent identifier
        if (commandRequest.getAgentIdentifier() == null || commandRequest.getAgentIdentifier().trim().isEmpty()) {
            log.warn("Command request missing agent identifier");
            return false;
        }

        // Validate agent type
        if (commandRequest.getAgentType() == null || commandRequest.getAgentType().trim().isEmpty()) {
            log.warn("Command request missing agent type");
            return false;
        }

        // Validate commands list
        if (commandRequest.getCommandsCount() == 0) {
            log.warn("Command request contains no commands");
            return false;
        }

        // Validate supervisor identifiers
        if (commandRequest.getSupervisorIdentifiersCount() == 0) {
            log.warn("Command request missing supervisor identifiers");
            return false;
        }

        // Validate trigger time
        if (commandRequest.getTriggerTime() <= 0) {
            log.warn("Command request has invalid trigger time: {}", commandRequest.getTriggerTime());
            return false;
        }

        // Validate individual commands
        for (CommandRequestProtos.Command command : commandRequest.getCommandsList()) {
            if (!isValidCommand(command)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validates an individual command within a CommandRequest.
     *
     * @param command the Command to validate
     * @return true if the command is valid, false otherwise
     */
    private boolean isValidCommand(CommandRequestProtos.Command command) {
        if (command == null) {
            log.warn("Command is null");
            return false;
        }

        if (command.getCommandJobId() == null || command.getCommandJobId().trim().isEmpty()) {
            log.warn("Command missing job ID");
            return false;
        }

        if (command.getCommandType() == null || command.getCommandType().trim().isEmpty()) {
            log.warn("Command missing command type");
            return false;
        }

        if (command.getCommand() == null || command.getCommand().trim().isEmpty()) {
            log.warn("Command missing command string");
            return false;
        }

        return true;
    }

    /**
     * Filters CommandRequest to include only supported forensic command types.
     *
     * This method creates a new CommandRequest containing only commands that are
     * supported by the forensic processing pipeline.
     *
     * @param originalRequest the original CommandRequest
     * @return filtered CommandRequest with only supported commands
     */
    private CommandRequestProtos.CommandRequest filterSupportedCommands(CommandRequestProtos.CommandRequest originalRequest) {
        CommandRequestProtos.CommandRequest.Builder filteredBuilder = CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier(originalRequest.getAgentIdentifier())
                .setAgentType(originalRequest.getAgentType())
                .setTriggerSource(originalRequest.getTriggerSource())
                .setTriggerTime(originalRequest.getTriggerTime())
                .addAllSupervisorIdentifiers(originalRequest.getSupervisorIdentifiersList());

        int supportedCommandCount = 0;
        for (CommandRequestProtos.Command command : originalRequest.getCommandsList()) {
            if (isSupportedCommandType(command.getCommandType())) {
                filteredBuilder.addCommands(command);
                supportedCommandCount++;
                log.debug("Added supported command: Type={}, JobId={}", 
                        command.getCommandType(), command.getCommandJobId());
            } else {
                log.warn("Unsupported command type ignored: Type={}, JobId={}", 
                        command.getCommandType(), command.getCommandJobId());
            }
        }

        log.info("Filtered {} supported commands out of {} total commands", 
                supportedCommandCount, originalRequest.getCommandsCount());

        return filteredBuilder.build();
    }

    /**
     * Checks if a command type is supported by the forensic processing pipeline.
     *
     * @param commandType the command type to check
     * @return true if the command type is supported, false otherwise
     */
    private boolean isSupportedCommandType(String commandType) {
        if (commandType == null || commandType.trim().isEmpty()) {
            return false;
        }

        return FORENSIC_COLLECTION.equals(commandType) ||
               FORENSIC_ANALYSIS.equals(commandType) ||
               FORENSIC_EXPORT.equals(commandType) ||
               FORENSIC_CLEANUP.equals(commandType);
    }

    /**
     * Gets the queue name for logging purposes.
     * Falls back to a default if the configuration is not available.
     *
     * @return the queue name
     */
    private String getQueueName() {
        try {
            // This would need to be configured similar to rabbitMqConfig.connectorMessagesQueue
            return "forensic-command-queue"; // Default fallback
        } catch (Exception e) {
            return "unknown-forensic-queue";
        }
    }

    /**
     * Health check method to verify receiver status.
     *
     * @return true if the receiver is healthy and ready to process messages
     */
    public boolean isHealthy() {
        try {
            return forensicDataCollectionHandler != null && 
                   healthMetrics != null && 
                   rabbitMqConfig != null;
        } catch (Exception e) {
            log.error("Health check failed for ForensicCommandRequestReceiver", e);
            return false;
        }
    }

    /**
     * Gets statistics about processed messages.
     *
     * @return string containing processing statistics
     */
    public String getProcessingStats() {
        try {
            return String.format("ForensicCommandRequestReceiver - Healthy: %s, Handler: %s", 
                    isHealthy(), 
                    forensicDataCollectionHandler != null ? "Available" : "Unavailable");
        } catch (Exception e) {
            return "ForensicCommandRequestReceiver - Stats unavailable: " + e.getMessage();
        }
    }
}
