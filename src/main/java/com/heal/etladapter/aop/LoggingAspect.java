package com.heal.etladapter.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Aspect
@Component
@Slf4j
public class LoggingAspect {

    @Pointcut("execution(* com.heal.*.initialize(..)) || " +
            "execution(* com.heal.*.extract(..)) || " +
            "execution(* com.heal.*.transform(..)) || " +
            "execution(* com.heal.*.load(..))")
    public void logExecutionMethods() {
    }

    @Around("logExecutionMethods()")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getSignature().getDeclaringTypeName();
        String methodName = joinPoint.getSignature().getName();

        Object[] args = joinPoint.getArgs();
        log.info("Entering method: {}.{} with arguments: {}", className, methodName, Arrays.toString(args));

        long startTime = System.currentTimeMillis();
        Object result;
        try {
            result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            if (log.isTraceEnabled()) {
                log.trace("Exiting method: {}.{} with result: {} | Time taken: {} ms", className, methodName, result, endTime - startTime);
            } else {
                log.info("Exiting method: {}.{} Time taken: {} ms", className, methodName, endTime - startTime);
            }
            return result;
        } catch (Throwable ex) {
            long endTime = System.currentTimeMillis();
            log.error("Exception in method: {}.{} with message: {} | Time taken: {} ms", className, methodName, ex.getMessage(), endTime - startTime);
            throw ex;
        }
    }
}
