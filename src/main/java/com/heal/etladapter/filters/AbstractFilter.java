package com.heal.etladapter.filters;

import com.heal.etladapter.utility.AdapterConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Data
@Component
public abstract class AbstractFilter<T> implements IFilter<T> {

    protected Map<String, String> parameters;
    protected String className;
    protected boolean isInitialized;
    protected boolean reloadConfig;
    protected RedisTemplate<String, Object> redisTemplate;
    protected JdbcTemplate jdbcTemplate;

    public void initialize() throws Exception {
        this.reloadConfig = false;
        if (this.parameters != null && this.parameters.containsKey(AdapterConstants.ADAPTER_WORKER_RELOADCONFIG))	{
            String val = this.parameters.get(AdapterConstants.ADAPTER_WORKER_RELOADCONFIG);
            if (val.equalsIgnoreCase("true"))	{
                this.reloadConfig = true;
                log.debug("Configuration reloading enabled for {}", this.className);
            }
        }
        if (!this.reloadConfig)	{
            log.debug("Configuration reloading disabled for {}", this.className);
        }
        this.isInitialized = true;
    }

    protected void reset()	{
        //Placeholder method
    }

    public void reload()	{
        try {
            if (reloadConfig)	{
                log.debug("Reloading configuration for {}", this.className);
                reset();
                initialize();
                log.debug("Reloading configuration for {} COMPLETE", this.className);
            }
        } catch (Exception e) {
            log.error("Error when reloading configuration for {}", this.className, e);
        }
    }
}
