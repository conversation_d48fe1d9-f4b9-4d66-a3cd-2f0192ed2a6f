package com.heal.etladapter.filters;

import com.heal.etladapter.pojos.HealAlert;
import com.heal.etladapter.repo.mysql.Heal48AlertsRepository;
import com.heal.etladapter.repo.mysql.HealAlertsExternalSystemRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class Heal48AlertsFilter extends AbstractFilter<HealAlert> {

	@Autowired
	Heal48AlertsRepository healRepo;

	@Autowired
	HealAlertsExternalSystemRepository extSystemRepo;

	@Override
	public Boolean dropItem(HealAlert alert) {
		if (alert == null) {
			log.info("Object to be filtered is null");
			return true;
		}

		if (Boolean.FALSE == healRepo.ruleExists(alert, this.jdbcTemplate)) {
			log.info("Heal Alert with ID {} and content {} dropped since its corresponding rule doesn't exist.",
					alert.getHealAlertId(), alert);
			return true;
		}

		if (extSystemRepo.healAlertExists(alert.getHealAlertId(), this.jdbcTemplate)) {
			log.info("Heal Alert with ID {} and content {} already pushed to external system; Hence ignoring now.",
					alert.getHealAlertId(), alert);
			return true;
		}

		return false;
	}

}
