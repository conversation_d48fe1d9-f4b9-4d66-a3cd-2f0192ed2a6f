package com.heal.etladapter.schedulers;

import com.heal.etladapter.beans.AdapterHealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MetricsScheduler {

    @Autowired
    private AdapterHealthMetrics healthMetrics;

    @Scheduled(initialDelay = 1000, fixedRateString = "${health.metrics.update.interval.milliseconds:10000}")
    public void updateSnapshots() {
        healthMetrics.resetSnapshots();
        log.debug("Metrics scheduler method called.");
    }
}
