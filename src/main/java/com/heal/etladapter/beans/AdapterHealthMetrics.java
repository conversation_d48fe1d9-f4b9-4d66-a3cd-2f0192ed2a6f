package com.heal.etladapter.beans;

import com.github.benmanes.caffeine.cache.Cache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@ManagedResource(objectName = "EtlAdapter:name=ApplicationInfo")
public class AdapterHealthMetrics {

    @Autowired
    @Lazy
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;
    @Autowired
    @Lazy
    private ThreadPoolTaskExecutor threadPoolExecutor;
    @Value("${redis.cache.mode:0}")
    int redisCacheMode;

    private int adapterExceptions = 0;
    private int rmqMessagesDropCount = 0;
    private int rmqMessagesReceivedCount;
    private int redisConfigurationErrorCount = 0;
    private int threadPoolTaskExecutorRejectedCount = 0;

    Map<String, Integer> transformerReceivedCount = new ConcurrentHashMap<>();
    Map<String, Integer> transformerDropCount = new ConcurrentHashMap<>();
    Map<String, Integer> transformerProcessedCount = new ConcurrentHashMap<>();
    Map<String, Integer> transformerErrors = new ConcurrentHashMap<>();

    Map<String, Integer> loaderReceivedCount = new ConcurrentHashMap<>();
    Map<String, Integer> loaderDropCount = new ConcurrentHashMap<>();
    Map<String, Integer> loaderProcessedCount = new ConcurrentHashMap<>();
    Map<String, Integer> loaderErrors = new ConcurrentHashMap<>();
    private final Map<String, Long> loaderHttpCallDetails = new ConcurrentHashMap<>();
    private final Map<String, Long> extractorHttpCallDetails = new ConcurrentHashMap<>();

    private Map<String, Integer> snapshots = new HashMap<>();
    private final Map<String, Cache<Object, Object>> cacheObjectMap = new HashMap<>();

    private int eventsDropCount;
    private int eventsReceivedCount;
    private int eventsProcessedCount;

    private int kpisDropCount;
    private int kpisReceivedCount;
    private int kpisProcessedCount;

    private int topologyEntitiesDropCount;
    private int topologyEntitiesReceivedCount;
    private int topologyEntitiesProcessedCount;

    private int transactionsDropCount;
    private int transactionsReceivedCount;
    private int transactionsProcessedCount;

    private final Map<String, Long> extractorErrors = new ConcurrentHashMap<>();

    @ManagedAttribute
    public int getTransactionsDropCount() {
        return transactionsDropCount;
    }

    public void updateTransactionsDropCount(int value) {
        this.transactionsDropCount = this.transactionsDropCount + value;
    }

    @ManagedAttribute
    public int getTransactionsReceivedCount() {
        return transactionsReceivedCount;
    }

    public void updateTransactionsReceivedCount(int value) {
        this.transactionsReceivedCount = this.transactionsReceivedCount + value;
    }

    @ManagedAttribute
    public int getTransactionsProcessedCount() {
        return transactionsProcessedCount;
    }

    public void updateTransactionsProcessedCount(int value) {
        this.transactionsProcessedCount = this.transactionsProcessedCount + value;
    }

    @ManagedAttribute
    public int getTopologyEntitiesDropCount() {
        return topologyEntitiesDropCount;
    }

    public void updateTopologyEntitiesDropCount(int value) {
        this.topologyEntitiesDropCount = this.topologyEntitiesDropCount + value;
    }

    @ManagedAttribute
    public int getTopologyEntitiesReceivedCount() {
        return topologyEntitiesReceivedCount;
    }

    public void updateTopologyEntitiesReceivedCount(int value) {
        this.topologyEntitiesReceivedCount = this.topologyEntitiesReceivedCount + value;
    }

    @ManagedAttribute
    public int getTopologyEntitiesProcessedCount() {
        return topologyEntitiesProcessedCount;
    }

    public void updateTopologyEntitiesProcessedCount(int value) {
        this.topologyEntitiesProcessedCount = this.topologyEntitiesProcessedCount + value;
    }

    @ManagedAttribute
    public Map<String, Long> getExtractorHttpCallDetails() {
        return extractorHttpCallDetails;
    }

    public void putInExtractorHttpCallDetails(String key, long value) {
        this.extractorHttpCallDetails.put(key, (extractorHttpCallDetails.getOrDefault(key, 0L) + value));
    }

    @ManagedAttribute
    public int getKpisDropCount() {
        return kpisDropCount;
    }

    public void updateKpisDropCount(int value) {
        this.kpisDropCount = this.kpisDropCount + value;
    }

    @ManagedAttribute
    public int getKpisReceivedCount() {
        return kpisReceivedCount;
    }

    public void updateKpisReceivedCount(int value) {
        this.kpisReceivedCount = this.kpisReceivedCount + value;
    }

    @ManagedAttribute
    public int getKpisProcessedCount() {
        return kpisProcessedCount;
    }

    public void updateKpisProcessedCount(int value) {
        this.kpisProcessedCount = this.kpisProcessedCount + value;
    }

    @ManagedAttribute
    public int getEventsReceivedCount() {
        return eventsReceivedCount;
    }

    public void updateExtractorReceivedCount(int value) {
        this.eventsReceivedCount = this.eventsReceivedCount + value;
    }

    @ManagedAttribute
    public int getRmqMessagesReceivedCount() {
        return rmqMessagesReceivedCount;
    }

    public void updateRmqMessagesReceivedCount(int value) {
        this.rmqMessagesReceivedCount = this.rmqMessagesReceivedCount + value;
    }

    @ManagedAttribute
    public int getThreadPoolTaskExecutorRejectedCount() {
        return threadPoolTaskExecutorRejectedCount;
    }

    public void updateThreadPoolTaskExecutorRejectedCount(int value) {
        this.threadPoolTaskExecutorRejectedCount = this.threadPoolTaskExecutorRejectedCount + value;
    }

    @ManagedAttribute
    public int getEventsDropCount() {
        return eventsDropCount;
    }

    public void updateEventDropCount(int value) {
        this.eventsDropCount = this.eventsDropCount + value;
    }

    @ManagedAttribute
    public Map<String, Long> getExtractorErrors() {
        return extractorErrors;
    }

    public void putInExtractorErrors(String key, int value) {
        this.extractorErrors.put(key, this.extractorErrors.getOrDefault(key, 0L) + value);
    }

    @ManagedAttribute
    public int getEventsProcessedCount() {
        return eventsProcessedCount;
    }

    public void updateExtractorProcessedCount(int value) {
        this.eventsProcessedCount = this.eventsProcessedCount + value;
    }
    @ManagedAttribute
    public Map<String, Long> getLoaderHttpCallDetails() {
        return loaderHttpCallDetails;
    }

    public void putInLoaderHttpCallDetails(String key, long value) {
        this.loaderHttpCallDetails.put(key, loaderHttpCallDetails.getOrDefault(key, 0L) + value);
    }

    @ManagedAttribute
    public int getRmqMessagesDropCount() {
        return this.rmqMessagesDropCount;
    }

    public void updateRmqMessagesDropCount() {
        this.rmqMessagesDropCount++;
    }

    public int getRedisConfigurationErrorCount() {
        return this.redisConfigurationErrorCount;
    }

    public void updateRedisConnectionErrorCount() {
        this.redisConfigurationErrorCount++;
    }

    @ManagedAttribute
    public int getAdapterExceptions() {
        return this.adapterExceptions;
    }

    public void updateAdapterExceptions() {
        this.adapterExceptions++;
    }

    @ManagedAttribute
    public Map<String, Integer> getTransformerReceivedCount() {
        return transformerReceivedCount;
    }

    public void putInTransformerReceivedCount(String key, int value) {
        transformerReceivedCount.put(key, transformerReceivedCount.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getTransformerDropCount() {
        return transformerDropCount;
    }

    public void putInTransformerDropCount(String key, int value) {
        transformerDropCount.put(key, transformerDropCount.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getTransformerProcessedCount() {
        return transformerProcessedCount;
    }

    public void putInTransformerProcessedCount(String key, int value) {
        transformerProcessedCount.put(key, transformerProcessedCount.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getTransformerErrors() {
        return transformerErrors;
    }

    public void putInTransformerErrors(String key, int value) {
        transformerErrors.put(key, transformerErrors.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getLoaderReceivedCount() {
        return loaderReceivedCount;
    }

    public void putInLoaderReceivedCount(String key, int value) {
        loaderReceivedCount.put(key, loaderReceivedCount.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getLoaderDropCount() {
        return loaderDropCount;
    }

    public void putInLoaderDropCount(String key, int value) {
        loaderDropCount.put(key, loaderDropCount.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getLoaderProcessedCount() {
        return loaderProcessedCount;
    }

    public void putInLoaderProcessedCount(String key, int value) {
        loaderProcessedCount.put(key, loaderProcessedCount.getOrDefault(key, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getLoaderErrors() {
        return loaderErrors;
    }

    public void putInLoaderErrors(String key, int value) {
        loaderErrors.put(key, loaderErrors.getOrDefault(key, 0) + value);
    }

    public void registerCacheObject(String cacheName, Cache<Object, Object> cache) {
        cacheObjectMap.put(cacheName, cache);
    }

    public String getCacheStats() {
        StringBuilder sb = new StringBuilder();
        cacheObjectMap.forEach((key, value) -> sb.append("{Cache name : ").append(key).append(", ")
                .append("Estimated size - ").append(value.estimatedSize()).append(", ").append(value.stats()).append("}\n"));
        return sb.toString();
    }

    public void resetSnapshots() {
        snapshots = new HashMap<>();
    }

    public void updateSnapshot(String keyName, int value) {
        snapshots.put(keyName, snapshots.getOrDefault(keyName, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getSnapshots() {
        return snapshots;
    }

    @ManagedAttribute
    public int getSchedulerPoolSize() {
        return threadPoolTaskScheduler.getPoolSize();
    }

    @ManagedAttribute
    public int getSchedulerActiveSize() {
        return threadPoolTaskScheduler.getActiveCount();
    }

    @ManagedAttribute
    public int getSchedulerQueueSize() {
        return threadPoolTaskScheduler.getScheduledThreadPoolExecutor().getQueue().size();
    }

    @ManagedAttribute
    public int getWorkerPoolSize() {
        return threadPoolExecutor.getPoolSize();
    }

    @ManagedAttribute
    public int getWorkerActiveSize() {
        return threadPoolExecutor.getActiveCount();
    }

    @ManagedAttribute
    public int getWorkerQueueSize() {
        return threadPoolExecutor.getThreadPoolExecutor().getQueue().size();
    }

    @Override
    public String toString() {
        return "Adapter exceptions count: " + getAdapterExceptions() + ", \n" +
                "RMQ messages drop count: " + getRmqMessagesDropCount() + ", \n" +
                "Transformers received counts: " + getTransformerReceivedCount() + ", \n" +
                "Transformers drop counts: " + getTransformerDropCount() + ", \n" +
                "Transformers processed counts: " + getTransformerProcessedCount() + ", \n" +
                "Transformers errors: " + getTransformerErrors() + ", \n" +
                "Loaders received counts: " + getLoaderReceivedCount() + ", \n" +
                "Loaders drop counts: " + getLoaderDropCount() + ", \n" +
                "Loaders processed counts: " + getLoaderProcessedCount() + ", \n" +
                "Loaders HTTP call counts: " + getLoaderHttpCallDetails() + ", \n" +
                "Loaders errors: " + getLoaderErrors() + ", \n" +
                "Current ETL adapter snapshots size : " + getSnapshots().size() + ", \n";
    }
}
