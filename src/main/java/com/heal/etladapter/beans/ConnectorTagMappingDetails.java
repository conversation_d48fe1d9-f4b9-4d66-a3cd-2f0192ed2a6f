package com.heal.etladapter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectorTagMappingDetails {
    private int id;
    private int tagId;
    private int objectId;
    private String objectRefTable;
    private String tagKey;
    private String tagValue;
    private int accountId;
    @Override
    public String toString() {
        return "TagMappingDetails{" +
                "tagId=" + tagId +
                ", objectId=" + objectId +
                ", objectRefTable='" + objectRefTable + '\'' +
                ", tagKey='" + tagKey + '\'' +
                ", tagValue='" + tagValue + '\'' +
                ", accountId=" + accountId +
                '}';
    }
}
