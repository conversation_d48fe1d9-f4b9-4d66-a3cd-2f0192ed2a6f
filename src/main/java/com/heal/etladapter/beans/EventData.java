package com.heal.etladapter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventData {
    private long eventStartTime;//2022-07-01 00:07:00
    private String eventId;
    private String eventType;//Workload or Behaviour
    private Integer kpiId;
    private String kpiIdentifier;
    @Builder.Default
    private String kpiAttribute = "ALL";
    private String categoryIdentifier;
    private String hostInstanceIdentifier;
    private long eventEndTime;
    private String applicationIdentifier;
    private String transactionIdentifier;
    private String serviceIdentifier;
    private String componentInstanceIdentifier;
    private double actualValue;
    private double anomalyScore;
    private String source;
    private String description;
    private boolean isGroupKpi;
    private boolean isTransactionEvent;
    private String incidentStatus;
    private Map<String, String> groupKpis;
    private String groupKpiIdentifier;

    @Builder.Default
    private String thresholdSeverity = "Default";
    private String extractorName;
    private String affectedEntityType;
    private long fromTime;
    private long toTime;
    private double threshold;
}
