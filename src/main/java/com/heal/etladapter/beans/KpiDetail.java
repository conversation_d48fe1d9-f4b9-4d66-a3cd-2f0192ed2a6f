package com.heal.etladapter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KpiDetail {
    private Integer id;
    private String name;
    private String description;
    private String dataType;
    private String isCustom;
    private String status;
    private String kpiTypeId;
    private String measureUnits;
    private String clusterOperation;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;
    private int kpiGroupId;
    private String identifier;
    private String valueType;
    private String rollUpOperation;
    private String clusterAggregationType;
    private String instanceAggregationType;
    private int isInformative;
    private int isComputed;
    private String cronExpression;
    private int deltaPerSec;
    private int resetDeltaValue;
}