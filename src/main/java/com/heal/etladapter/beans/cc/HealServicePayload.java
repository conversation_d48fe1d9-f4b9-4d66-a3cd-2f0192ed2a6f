package com.heal.etladapter.beans.cc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealServicePayload implements IPayload {

    private String name;
    private String identifier;
    private String layer;
    private String timezone;
    private Set<String> appIdentifiers;


}
