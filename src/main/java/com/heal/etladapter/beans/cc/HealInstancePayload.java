package com.heal.etladapter.beans.cc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealInstancePayload implements IPayload {
    private String name;
    private String identifier;
    private String componentName;
    private String componentVersion;
    private Set<String> serviceIdentifiers;
    private String parentIdentifier;
    private List<String> agentIdentifiers;
    private int discovery;
    private List<Attributes> attributes;
    private String environment;
}
