package com.heal.etladapter.beans.cc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealAgentPayload implements IPayload {
    @Builder.Default
    private int id = 0;
    private String name;
    private String uniqueToken;
    @Builder.Default
    private String description = "";
    private String subType;
    @Builder.Default
    private String mode = "MONITOR";
    private String hostAddress;
    private int serviceId;
    private String serviceName;
    @Builder.Default
    private int status = 1;
    private AgentMappingDetails agentMappingDetails;
    private ArrayList<String> addedDataSources;
}
