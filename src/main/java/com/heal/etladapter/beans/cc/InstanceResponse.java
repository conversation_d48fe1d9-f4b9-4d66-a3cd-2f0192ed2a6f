package com.heal.etladapter.beans.cc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class InstanceResponse {
    private String message;
    private String responseStatus;
    private List<Data> data;

    @Getter
    @Setter
    public static class Data {
        private int id;
        private String name;
        private String identifier;
    }

}