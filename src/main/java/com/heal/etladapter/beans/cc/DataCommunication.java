package com.heal.etladapter.beans.cc;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCommunication {
    @Builder.Default
    private String protocol = "GRPC";
    private String host;
    private String port;
    @Builder.Default
    private String type = "Communication_Endpoint";
    @Builder.Default
    private String name = "GRPC";
    @Builder.Default
    private String description = "GRPC";
}
