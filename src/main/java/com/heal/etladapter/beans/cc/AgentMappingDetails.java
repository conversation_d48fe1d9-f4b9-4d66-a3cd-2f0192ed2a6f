package com.heal.etladapter.beans.cc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentMappingDetails {
    @Builder.Default
    private int timeoutMultiplier = 2;
    @Builder.Default
    private String configOperationMode = "Remote";
    @Builder.Default
    private String dataOperationMode = "Remote";
    DataCommunication dataCommunication;
}


