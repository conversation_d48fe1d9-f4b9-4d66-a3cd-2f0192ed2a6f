package com.heal.etladapter.transformers;

import com.heal.etladapter.exceptions.EtlAdapterException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Data
@Component
public abstract class AbstractTransformer<T, U> {

    protected String connectorInstanceIdentifier;
    protected String jobId;
    protected Map<String, String> parameters;
    protected String className;
    protected int order;
    protected RedisTemplate<String, Object> redisTemplate;
    protected JdbcTemplate jdbcTemplate;

    public abstract void initialize() throws EtlAdapterException;

    public abstract U transform(T item) throws EtlAdapterException;
}
