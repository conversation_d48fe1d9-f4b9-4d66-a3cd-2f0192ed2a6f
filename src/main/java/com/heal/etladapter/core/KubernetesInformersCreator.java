package com.heal.etladapter.core;

import com.heal.etladapter.pojos.enums.EventAction;
import com.heal.etladapter.utility.KubernetesTopologyCache;
import io.kubernetes.client.informer.ResourceEventHandler;
import io.kubernetes.client.informer.SharedIndexInformer;
import io.kubernetes.client.informer.SharedInformerFactory;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1NodeList;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodList;
import io.kubernetes.client.util.CallGeneratorParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
public class KubernetesInformersCreator {

    private final AtomicBoolean informersCreation = new AtomicBoolean(false);

    private SharedInformerFactory informerFactory;

    @Autowired
    KubernetesTopologyCache cache;

    public void startNodePodInformers(ApiClient apiClient) throws Exception {
        if (informersCreation.compareAndSet(false, true)) {
            createInformers(apiClient);
            informerFactory.startAllRegisteredInformers();
        }
    }

    private void createInformers(ApiClient apiClient) throws Exception {
        CoreV1Api coreV1Api = new CoreV1Api();

        informerFactory = new SharedInformerFactory(apiClient);

        SharedIndexInformer<V1Node> nodeInformer = informerFactory.sharedIndexInformerFor(
                (CallGeneratorParams params) -> coreV1Api.listNode().resourceVersion(params.resourceVersion).timeoutSeconds(params.timeoutSeconds).watch(true).buildCall(null),
                V1Node.class,
                V1NodeList.class);

        nodeInformer.addEventHandler(new ResourceEventHandler<>() {
            @Override
            public void onAdd(V1Node node) {
                if(isNodeReady(node)) {
                    cache.addToNodesQueue(EventAction.ADD.name(), node);
                } else {
                    log.warn("Node is not in ready state. Details: {}", node);
                }
            }

            @Override
            public void onUpdate(V1Node oldNode, V1Node newNode) {
                cache.addToNodesQueue(EventAction.MODIFY.name(), newNode);
            }

            @Override
            public void onDelete(V1Node node, boolean deletedFinalStateUnknown) {
                cache.addToNodesQueue(EventAction.DELETE.name(), node);
            }
        });

        SharedIndexInformer<V1Pod> podInformer = informerFactory.sharedIndexInformerFor(
                (CallGeneratorParams params) -> coreV1Api.listPodForAllNamespaces().resourceVersion(params.resourceVersion).timeoutSeconds(params.timeoutSeconds).watch(true).buildCall(null),
                V1Pod.class,
                V1PodList.class);

        podInformer.addEventHandler(new ResourceEventHandler<>() {
            @Override
            public void onAdd(V1Pod pod) {
                if (isPodReady(pod)) {
                    cache.addToPodsQueue(EventAction.ADD.name(), pod);
                } else {
                    log.warn("Pod is not in ready state. Details: {}", pod);
                }
            }

            @Override
            public void onUpdate(V1Pod oldPod, V1Pod newPod) {
                cache.addToPodsQueue(EventAction.MODIFY.name(), newPod);
            }

            @Override
            public void onDelete(V1Pod pod, boolean deletedFinalStateUnknown) {
                cache.addToPodsQueue(EventAction.DELETE.name(), pod);
            }
        });
    }

    public boolean isPodReady(V1Pod pod) {
        return pod != null && pod.getMetadata() != null && pod.getMetadata().getNamespace() != null
                && pod.getMetadata().getName() != null && pod.getStatus() != null && pod.getStatus().getPodIP() != null
                && (pod.getStatus().getContainerStatuses() != null || !pod.getStatus().getContainerStatuses().isEmpty()
                && pod.getStatus().getPhase() != null && pod.getStatus().getPhase().equalsIgnoreCase("Running"));
    }

    /** Utility method to check if at least one Node condition has type=Ready and status=True. */
    private boolean isNodeReady(V1Node node) {
        if (node.getStatus() != null && node.getStatus().getConditions() != null) {
            return node.getStatus().getConditions().stream()
                    .anyMatch(cond ->
                            "Ready".equals(cond.getType()) &&
                                    "True".equals(cond.getStatus())
                    );
        }
        return false;
    }
}
