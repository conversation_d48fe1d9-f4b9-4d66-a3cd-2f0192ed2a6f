/*
package com.heal.etladapter.junit;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.heal.etladapter.chain.AbstractChainableWorker;
import com.heal.etladapter.chain.AdapterChain;
import com.heal.etladapter.etl.common.preprocessor.NullPreprocessor;
import com.heal.etladapter.core.extractors.queue.MessageQueueExtractor;
import com.heal.etladapter.core.loaders.MessageQueueLoader;
import com.heal.etladapter.core.transformers.PassThroughTransformer;
import com.heal.etladapter.core.loaders.ServiceNowTicketLoader;
import com.heal.etladapter.core.extractors.query.Heal48AlertsExtractor;
import com.heal.etladapter.core.filters.Heal48AlertsFilter;
import com.heal.etladapter.repo.mysql.Heal48AlertsRepository;
import com.heal.etladapter.repo.mysql.HealAlertsExternalSystemRepository;
import com.heal.etladapter.core.transformers.HealAlertsToSnowTicketTransformer;
import com.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster;
import com.heal.etladapter.pojos.AdapterConfiguration;
import com.heal.etladapter.repositories.AbstractRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import com.heal.etladapter.utility.Constants;

import com.heal.etladapter.etl.logscan.constants.LogscanConstants;
import com.heal.etladapter.etl.logscan.extractors.LogscanLAKPIExtractor;
import com.heal.etladapter.etl.common.filters.LogscanLAKPIFilter;
import com.heal.etladapter.etl.heal.loaders.HealKPIGrpcLoader;
import com.heal.etladapter.etl.heal.transformers.HealKPITransformer;
import com.heal.etladapter.etl.logscan.extractors.LogscanTransactionExtractor;
import com.heal.etladapter.etl.heal.loaders.HealTransactionGrpcLoader;
import com.heal.etladapter.etl.heal.transformers.HealTransactionTransformer;

import com.heal.etladapter.utility.DateHelper;
import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import io.reactivex.BackpressureStrategy;

@Slf4j
public class ConfigurationGeneratorTest {

	@Test
	public void passThroughConfig() {

		try {

			String filename = "conf/adapter-passthrough.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generatePassThroughConfig());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.setCsvDir("logs/");
			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (IOException e) {
			log.error("error occurred in passThroughConfig method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	@Test
	public void generateLALTMKpizConfigurationNoMQExtraction4Prod() {

		try {

			String filename = "conf/adapter-heal-dev.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generateLTMKpisConfigNoMQ4Prod());
			chain.add(generateLaKpisConfig4Heal());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("/opt/heal/ext-system-adapter/logs/");
			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (IOException e) {
			log.error("error occurred in generateLALTMKpizConfigurationNoMQExtraction4Prod method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	
	@Test
	public void generateLALTMKpizConfigurationNoMQExtraction4Dev() {

		try {

			String filename = "conf/adapter2.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<AdapterChain>();
			chain.add(generateLTMKpisConfigNoMQ());
			chain.add(generateLaKpisConfig());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("logs/");
			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (JsonGenerationException e) {
			log.error("JsonGenerationException occurred in generateLALTMKpizConfigurationNoMQExtraction4Dev method. Details: ",e);
		} catch (JsonMappingException e) {
			log.error("JsonMappingException occurred in generateLALTMKpizConfigurationNoMQExtraction4Dev method. Details: ",e);
		} catch (IOException e) {
			log.error("IOException occurred in generateLALTMKpizConfigurationNoMQExtraction4Dev method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	@Test
	public void generateLALTMKpizConfigurationForHeal() {

		try {

			String filename = "conf/adapter-heal.yaml";

			AdapterConfiguration config = new AdapterConfiguration();
			config.setCsvDir("/opt/heal/ext-system-adapter/logs/");

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generateLTMKpisConfig4Heal());
			chain.add(generateLaKpisConfig4Heal());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("logs/");

			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (JsonGenerationException e) {
			log.error("JsonGenerationException occurred in generateLALTMKpizConfigurationForHeal method. Details: ",e);
		} catch (JsonMappingException e) {
			log.error("JsonMappingException occurred in generateLALTMKpizConfigurationForHeal method. Details: ",e);
		} catch (IOException e) {
			log.error("IOException occurred in generateLALTMKpizConfigurationForHeal method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	
	@Test
	public void generateLALTMKpizConfiguration() {

		try {

			String filename = "conf/adapter.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generateLTMKpisConfig());
			chain.add(generateLaKpisConfig());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("logs/");

			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (JsonGenerationException e) {
			log.error("JsonGenerationException occurred in generateLALTMKpizConfiguration method. Details: ",e);
		} catch (JsonMappingException e) {
			log.error("JsonMappingException occurred in generateLALTMKpizConfiguration method. Details: ",e);
		} catch (IOException e) {
			log.error("IOException occurred in generateLALTMKpizConfiguration method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	@Test
	public void generateLTMKpizConfiguration() {

		try {

			String filename = "conf/logscan-ltmpis.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generateLTMKpisConfig());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("Heal48AlertsRepository", getHeal48AlertsRepository());
			config.getRepositories().put("HealAlertsExternalSystemRepository", getHealAlertsExternalSystemRepository());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("logs/");

			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (JsonGenerationException e) {
			log.error("JsonGenerationException occurred in generateLTMKpizConfiguration method. Details: ",e);
		} catch (JsonMappingException e) {
			log.error("JsonMappingException occurred in generateLTMKpizConfiguration method. Details: ",e);
		} catch (IOException e) {
			log.error("IOException occurred in generateLTMKpizConfiguration method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	@Test
	public void generateLAKpizConfiguration() {

		try {

			String filename = "conf/logscan-lakpis.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generateLaKpisConfig());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("Heal48AlertsRepository", getHeal48AlertsRepository());
			config.getRepositories().put("HealAlertsExternalSystemRepository", getHealAlertsExternalSystemRepository());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("logs/");

			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (JsonGenerationException e) {
			log.error("JsonGenerationException occurred in generateLTMKpizConfiguration method. Details: ",e);
		} catch (JsonMappingException e) {
			log.error("JsonMappingException occurred in generateLTMKpizConfiguration method. Details: ",e);
		} catch (IOException e) {
			log.error("IOException occurred in generateLTMKpizConfiguration method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	@Test
	public void generateSnowConfiguration() {

		try {
			String filename = "conf/cs-snow.yaml";

			AdapterConfiguration config = new AdapterConfiguration();

			List<AdapterChain> chain = new ArrayList<>();
			chain.add(generateHealAlertsConfig());
			ObjectMapper om = new ObjectMapper(new YAMLFactory());

			config.setChains(chain);
			config.setRepositories(new HashMap<>());
			config.getRepositories().put("Heal48AlertsRepository", getHeal48AlertsRepository());
			config.getRepositories().put("HealAlertsExternalSystemRepository", getHealAlertsExternalSystemRepository());
			config.getRepositories().put("LogAnalyzerKPIMaster", getMasterDataRepository());
			config.setCsvDir("logs/");

			om.writeValue(new File(filename), config);

			System.out.println("Now reading...");

			readShit(filename);
		} catch (JsonGenerationException e) {
			log.error("JsonGenerationException occurred in generateLTMKpizConfiguration method. Details: ",e);
		} catch (JsonMappingException e) {
			log.error("JsonMappingException occurred in generateLTMKpizConfiguration method. Details: ",e);
		} catch (IOException e) {
			log.error("IOException occurred in generateLTMKpizConfiguration method. Details: ",e);
		}

		System.out.println("Done dona done done");
	}

	public static void readShit(String filename) throws IOException {
		File file = new File(filename);

		// Instantiating a new ObjectMapper as a YAMLFactory
		ObjectMapper om = new ObjectMapper(new YAMLFactory());

		AdapterConfiguration config = om.readValue(file, AdapterConfiguration.class);
		config.getRepositories().forEach((key, value) -> value.initialize());
		System.out.println(config);
	}

	public static AdapterChain generateHealAlertsConfig() {
		AdapterChain chain = new AdapterChain();
		chain.setChainId("Heal 4.8 Alerts to Service Now");
		chain.setExtractor(new Heal48AlertsExtractor());

		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(Heal48AlertsExtractor.class.getName());
		extractorParams.put(Constants.ADAPTER_REPOSITORY, "Heal48AlertsRepository");
		extractorParams.put("account.name", "");
		chain.getExtractor().setParameters(extractorParams);
		chain.setSleepInterval(60);
		chain.setChainOfWorkers(new ArrayList<>());

		Heal48AlertsFilter filter = new Heal48AlertsFilter();
		Map<String, String> fitlerParams = new HashMap<>();
		filter.setClassName(Heal48AlertsFilter.class.getName());
		fitlerParams.put(Constants.ADAPTER_REPOSITORY, "Heal48AlertsRepository");
		fitlerParams.put("account.name", "");
		filter.setParameters(fitlerParams);

		HealAlertsToSnowTicketTransformer transformer = new HealAlertsToSnowTicketTransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformer.setClassName(HealAlertsToSnowTicketTransformer.class.getName());
		transformer.setParameters(transformerParams);

		ServiceNowTicketLoader loader = new ServiceNowTicketLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loaderParams.put(Constants.ADAPTER_REPOSITORY, "HealAlertsExternalSystemRepository");
		loader.setClassName(ServiceNowTicketLoader.class.getName());
		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(filter);
		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}

	public static AdapterChain generatePassThroughConfig() {

		AdapterChain chain = new AdapterChain();
		chain.setChainId("Pass Through Chain");
		chain.setProcessorThreadPoolSize(2);
		chain.setExtractor(new MessageQueueExtractor());
		chain.setAddSysOutLoader(true);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(MessageQueueExtractor.class.getName());
		extractorParams.put(Constants.MQ_CONFIG_HOST, "127.0.0.1");
		extractorParams.put(Constants.MQ_CONFIG_EXCHANGE, "LogscanTxnData");
		extractorParams.put(Constants.MQ_CONFIG_EXCHANGE_TYPE, "direct");
		extractorParams.put(Constants.MQ_CONFIG_ROUTINGKEY, "LTMKPI");
		extractorParams.put(Constants.MQ_CONFIG_QUEUE, "LogscanTxnQueue");
		extractorParams.put(Constants.MQ_CONFIG_USERNAME, "guest");
		extractorParams.put(Constants.MQ_CONFIG_PASSWORD, "guest");
		extractorParams.put(Constants.MQ_CONFIG_PORT, "5672");
		extractorParams.put(Constants.ADAPTER_LISTENINGEXTRACTOR_MAXRETRY, "-1");
		extractorParams.put(Constants.ADAPTER_LISTENINGEXTRACTOR_RETRYINTERVAL, "60");

		chain.getExtractor().setParameters(extractorParams);
		chain.setChainOfWorkers(new ArrayList<>());

		PassThroughTransformer transformer = new PassThroughTransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformer.setClassName(PassThroughTransformer.class.getName());
		transformer.setParameters(transformerParams);

		MessageQueueLoader loader = new MessageQueueLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loader.setClassName(MessageQueueLoader.class.getName());
		loaderParams.put(Constants.MQ_CONFIG_EXCHANGE, "HealLTMExchange");
		loaderParams.put(Constants.MQ_CONFIG_EXCHANGE_TYPE, "direct");
		loaderParams.put(Constants.MQ_CONFIG_ROUTINGKEY, "HealLTM");
		loaderParams.put(Constants.MQ_CONFIG_QUEUE, "HealLTMQueue");
		loaderParams.put(Constants.MQ_CONFIG_USERNAME, "guest");
		loaderParams.put(Constants.MQ_CONFIG_PASSWORD, "guest");
		loaderParams.put(Constants.MQ_CONFIG_PORT, "5672");
		loaderParams.put(Constants.MQ_CONFIG_HOST, "127.0.0.1");

		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}
	
	public static AdapterChain generateLTMKpisConfig() {

		AdapterChain chain = new AdapterChain();
		chain.setChainId("Logscan LTM KPIs");
		chain.setProcessorThreadPoolSize(2);
		chain.setLoaderThreadPoolSize(3);
		chain.setExtractor(new MessageQueueExtractor());
		chain.setAddSysOutLoader(true);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(MessageQueueExtractor.class.getName());
		extractorParams.put(Constants.MQ_CONFIG_HOST, "127.0.0.1");
		extractorParams.put(Constants.MQ_CONFIG_EXCHANGE, "LogscanTxnData");
		extractorParams.put(Constants.MQ_CONFIG_EXCHANGE_TYPE, "direct");
		extractorParams.put(Constants.MQ_CONFIG_ROUTINGKEY, "LTMKPI");
		extractorParams.put(Constants.MQ_CONFIG_QUEUE, "LogscanTxnQueue");
		extractorParams.put(Constants.MQ_CONFIG_USERNAME, "guest");
		extractorParams.put(Constants.MQ_CONFIG_PASSWORD, "guest");
		extractorParams.put(Constants.MQ_CONFIG_PORT, "5672");
		extractorParams.put(Constants.ADAPTER_LISTENINGEXTRACTOR_MAXRETRY, "-1");
		extractorParams.put(Constants.ADAPTER_LISTENINGEXTRACTOR_RETRYINTERVAL, "60");

		chain.getExtractor().setParameters(extractorParams);
		chain.setChainOfWorkers(new ArrayList<AbstractChainableWorker>());

		HealTransactionTransformer transformer = new HealTransactionTransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformer.setClassName(HealTransactionTransformer.class.getName());
		transformerParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		transformer.setParameters(transformerParams);

		MessageQueueLoader loader = new MessageQueueLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loader.setClassName(MessageQueueLoader.class.getName());
		loaderParams.put(Constants.MQ_CONFIG_EXCHANGE, "HealLTMExchange");
		loaderParams.put(Constants.MQ_CONFIG_EXCHANGE_TYPE, "direct");
		loaderParams.put(Constants.MQ_CONFIG_ROUTINGKEY, "HealLTM");
		loaderParams.put(Constants.MQ_CONFIG_QUEUE, "HealLTMQueue");
		loaderParams.put(Constants.MQ_CONFIG_USERNAME, "guest");
		loaderParams.put(Constants.MQ_CONFIG_PASSWORD, "guest");
		loaderParams.put(Constants.MQ_CONFIG_PORT, "5672");
		loaderParams.put(Constants.MQ_CONFIG_HOST, "127.0.0.1");

		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}

	public static AdapterChain generateLaKpisConfig() {
		AdapterChain chain = new AdapterChain();
		chain.setChainId("Logscan LA KPIs");
		chain.setProcessorThreadPoolSize(2);
		chain.setLoaderThreadPoolSize(2);
		chain.setAddSysOutLoader(true);
		chain.setDisableChain(true);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		chain.setTestEndDate(new Date());
		chain.setTestStartDate(DateHelper.string2dateConverter("2018-01-01 00:00:00")); // yyyy-MM-dd HH:mm:ss
		chain.setExtractor(new LogscanLAKPIExtractor());
		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(LogscanLAKPIExtractor.class.getName());
		extractorParams.put(LogscanConstants.LOGSCAN_ENDPT_ID, "6878");
		extractorParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_MAXATTEMPTS, "-1");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_RETRYINTERVAL, "60");

		chain.getExtractor().setParameters(extractorParams);
		chain.setSleepInterval(60);
		chain.setChainOfWorkers(new ArrayList<>());

		LogscanLAKPIFilter filter = new LogscanLAKPIFilter();
		Map<String, String> fitlerParams = new HashMap<>();
		filter.setClassName(LogscanLAKPIFilter.class.getName());
		filter.setParameters(fitlerParams);

		HealKPITransformer transformer = new HealKPITransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformerParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		transformer.setClassName(HealKPITransformer.class.getName());
		transformer.setParameters(transformerParams);

		MessageQueueLoader loader = new MessageQueueLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loader.setClassName(MessageQueueLoader.class.getName());
		loaderParams.put(Constants.MQ_CONFIG_EXCHANGE, "HealNoGrpcLaKPIxChange");
		loaderParams.put(Constants.MQ_CONFIG_EXCHANGE_TYPE, "direct");
		loaderParams.put(Constants.MQ_CONFIG_ROUTINGKEY, "LAKPI");
		loaderParams.put(Constants.MQ_CONFIG_QUEUE, "HealNoGrpcLaKPIQueue");
		loaderParams.put(Constants.MQ_CONFIG_USERNAME, "guest");
		loaderParams.put(Constants.MQ_CONFIG_PASSWORD, "guest");
		loaderParams.put(Constants.MQ_CONFIG_PORT, "5672");
		loaderParams.put(Constants.MQ_CONFIG_HOST, "127.0.0.1");

		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(filter);
		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}

	
	
	public static AdapterChain generateLTMKpisConfig4Heal() {

		AdapterChain chain = new AdapterChain();
		chain.setChainId("Logscan LTM KPI to Heal");
		chain.setProcessorThreadPoolSize(2);
		chain.setLoaderThreadPoolSize(3);
		chain.setExtractor(new MessageQueueExtractor());
		chain.setAddSysOutLoader(true);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		chain.setBackPressureMaxSize(1024);
		chain.setDisableChain(false);
		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(MessageQueueExtractor.class.getName());
		extractorParams.put(Constants.MQ_CONFIG_HOST, "127.0.0.1");
		extractorParams.put(Constants.MQ_CONFIG_EXCHANGE, "LogscanTxnData");
		extractorParams.put(Constants.MQ_CONFIG_EXCHANGE_TYPE, "direct");
		extractorParams.put(Constants.MQ_CONFIG_ROUTINGKEY, "LTMKPI");
		extractorParams.put(Constants.MQ_CONFIG_QUEUE, "LogscanTxnQueue");
		extractorParams.put(Constants.MQ_CONFIG_PASSWORD, "guest");
		extractorParams.put(Constants.MQ_CONFIG_PORT, "5672");
		extractorParams.put(Constants.ADAPTER_LISTENINGEXTRACTOR_MAXRETRY, "-1");
		extractorParams.put(Constants.ADAPTER_LISTENINGEXTRACTOR_RETRYINTERVAL, "60");

		chain.getExtractor().setParameters(extractorParams);
		chain.setChainOfWorkers(new ArrayList<AbstractChainableWorker>());

		HealTransactionTransformer transformer = new HealTransactionTransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformer.setClassName(HealTransactionTransformer.class.getName());
		transformerParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		transformer.setParameters(transformerParams);

		HealTransactionGrpcLoader loader = new HealTransactionGrpcLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loaderParams.put(Constants.HEAL_SSL_ENABLED, "true");
		loaderParams.put(Constants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert");
		loaderParams.put(Constants.HEAL_GRPCENDPT_ADDR, "grpc.appnomic");
		loaderParams.put(Constants.HEAL_GRPCENDPT_PORT, "11000");
		loaderParams.put(Constants.HEAL_RETRY_COUNT, "-1");
		loaderParams.put(Constants.HEAL_RETRY_INTERVAL, "60");

		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}

	public static AdapterChain generateLaKpisConfig4Heal() {
		AdapterChain chain = new AdapterChain();
		chain.setChainId("Logscan LA KPIs");
		chain.setProcessorThreadPoolSize(2);
		chain.setLoaderThreadPoolSize(2);
		chain.setAddSysOutLoader(true);
		chain.setTestEndDate(new Date());
		chain.setDisableChain(false);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		chain.setBackPressureMaxSize(1024);
		chain.setReloadIntervalInMin(1);
		chain.setPreProcessor(new NullPreprocessor());
		
		chain.setTestStartDate(DateHelper.string2dateConverter("2018-01-01 00:00:00")); // yyyy-MM-dd HH:mm:ss
		chain.setExtractor(new LogscanLAKPIExtractor());
		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(LogscanLAKPIExtractor.class.getName());
		extractorParams.put(LogscanConstants.LOGSCAN_ENDPT_ID, "6878");
		extractorParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_MAXATTEMPTS, "-1");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_RETRYINTERVAL, "60");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_CONNTIMEOUT, "10000");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_SOCKETTIMEOUT, "900000");
		extractorParams.put(Constants.ADAPTER_WORKER_RELOADCONFIG, "true");

		chain.getExtractor().setParameters(extractorParams);
		chain.setSleepInterval(60);
		chain.setChainOfWorkers(new ArrayList<>());

		LogscanLAKPIFilter filter = new LogscanLAKPIFilter();
		Map<String, String> fitlerParams = new HashMap<>();
		filter.setClassName(LogscanLAKPIFilter.class.getName());
		filter.setParameters(fitlerParams);

		HealKPITransformer transformer = new HealKPITransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformerParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		transformerParams.put(Constants.ADAPTER_WORKER_RELOADCONFIG, "true");
		transformer.setClassName(HealKPITransformer.class.getName());
		transformer.setParameters(transformerParams);
	

		HealKPIGrpcLoader loader = new HealKPIGrpcLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loaderParams.put(Constants.HEAL_SSL_ENABLED, "true");
		loaderParams.put(Constants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert");
		loaderParams.put(Constants.HEAL_GRPCENDPT_ADDR, "grpc.appnomic");
		loaderParams.put(Constants.HEAL_GRPCENDPT_PORT, "11000");
		loaderParams.put(Constants.HEAL_RETRY_COUNT, "-1");
		loaderParams.put(Constants.HEAL_RETRY_INTERVAL, "60");

		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(filter);
		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}

	public static AdapterChain generateLTMKpisConfigNoMQ() {

		AdapterChain chain = new AdapterChain();
		chain.setChainId("Logscan LTM KPIs");
		chain.setProcessorThreadPoolSize(2);
		chain.setLoaderThreadPoolSize(3);
		chain.setExtractor(new LogscanTransactionExtractor());
		chain.setAddSysOutLoader(true);
		chain.setDisableChain(false);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		chain.setBackPressureMaxSize(50000);
		chain.setSleepInterval(60);

		chain.setTestEndDate(DateHelper.string2dateConverter("2019-08-06 00:00:00"));
		chain.setTestStartDate(DateHelper.string2dateConverter("2019-08-05 00:00:00")); // yyyy-MM-dd HH:mm:ss

		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(LogscanTransactionExtractor.class.getName());
		extractorParams.put(LogscanConstants.LOGSCAN_ENDPT_ID, "6878");
		extractorParams.put(LogscanConstants.LOGSCAN_INDEX_PATTERN, "logscan-raw*-acl-*");
		extractorParams.put(LogscanConstants.LOGSCAN_FIELD_EXISTS_PATTERN, "http.response.time,http.uri");
		extractorParams.put(LogscanConstants.LOGSCAN_FIELD_EXISTS_PATTERN_SEPARATOR, ",");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_MAXATTEMPTS, "-1");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_RETRYINTERVAL, "60");
		extractorParams.put(LogscanConstants.LOGSCAN_QUERY_SCROLLSIZE, "500");
		extractorParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");

		chain.getExtractor().setParameters(extractorParams);
		chain.setChainOfWorkers(new ArrayList<>());

		HealTransactionTransformer transformer = new HealTransactionTransformer();
		Map<String, String> transformerParams = new HashMap<String, String>();
		transformer.setClassName(HealTransactionTransformer.class.getName());
		transformerParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		transformer.setParameters(transformerParams);

		chain.getChainOfWorkers().add(transformer);
		return chain;
	}
	
	public static AdapterChain generateLTMKpisConfigNoMQ4Prod() {

		AdapterChain chain = new AdapterChain();
		chain.setChainId("Logscan LTM KPIs");
		chain.setProcessorThreadPoolSize(2);
		chain.setLoaderThreadPoolSize(3);
		chain.setExtractor(new LogscanTransactionExtractor());
		chain.setAddSysOutLoader(true);
		chain.setDisableChain(false);
		chain.setBackPressureStrategy(BackpressureStrategy.BUFFER);
		chain.setBackPressureMaxSize(500000);
		chain.setSleepInterval(60);
		chain.setReloadIntervalInMin(1);

		Map<String, String> extractorParams = new HashMap<>();
		chain.getExtractor().setClassName(LogscanTransactionExtractor.class.getName());
		extractorParams.put(LogscanConstants.LOGSCAN_ENDPT_ID, "6878");
		extractorParams.put(LogscanConstants.LOGSCAN_INDEX_PATTERN, "logscan-raw*-acl-*");
		extractorParams.put(LogscanConstants.LOGSCAN_FIELD_EXISTS_PATTERN, "http.response.time,http.uri");
		extractorParams.put(LogscanConstants.LOGSCAN_FIELD_EXISTS_PATTERN_SEPARATOR, ",");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_MAXATTEMPTS, "-1");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_RETRYINTERVAL, "60");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_CONNTIMEOUT, "10000");
		extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_SOCKETTIMEOUT, "900000");
		extractorParams.put(LogscanConstants.LOGSCAN_QUERY_SCROLLSIZE, "500");
		extractorParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
		extractorParams.put(LogscanConstants.LOGSCAN_QUERY_FILTER_START + "http.uri", ".png,.css,.gif");
		extractorParams.put(Constants.ADAPTER_WORKER_RELOADCONFIG, "true");
		

		chain.getExtractor().setParameters(extractorParams);
		chain.setChainOfWorkers(new ArrayList<>());

		HealTransactionTransformer transformer = new HealTransactionTransformer();
		Map<String, String> transformerParams = new HashMap<>();
		transformer.setClassName(HealTransactionTransformer.class.getName());
		transformerParams.put(Constants.ADAPTER_WORKER_RELOADCONFIG, "true");
		
		transformer.setParameters(transformerParams);
		
		
		HealTransactionGrpcLoader loader = new HealTransactionGrpcLoader();
		Map<String, String> loaderParams = new HashMap<>();
		loaderParams.put(Constants.HEAL_SSL_ENABLED, "true");
		loaderParams.put(Constants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert");
		loaderParams.put(Constants.HEAL_GRPCENDPT_ADDR, "grpc.appnomic");
		loaderParams.put(Constants.HEAL_GRPCENDPT_PORT, "11000");
		loaderParams.put(Constants.HEAL_RETRY_COUNT, "-1");
		loaderParams.put(Constants.HEAL_RETRY_INTERVAL, "60");

		loader.setParameters(loaderParams);

		chain.getChainOfWorkers().add(transformer);
		chain.getChainOfWorkers().add(loader);
		return chain;
	}

	public static AbstractRepository getHeal48AlertsRepository() {
		Heal48AlertsRepository repo = new Heal48AlertsRepository();
		Map<String, String> adapterDbProps = new HashMap<>();
		adapterDbProps.put("hibernate.connection.url", "***************************/");
		adapterDbProps.put("hibernate.connection.username", "appsone");
		adapterDbProps.put("hibernate.connection.password", "password");
		adapterDbProps.put("hibernate.c3p0.min_size", "1");
		adapterDbProps.put("hibernate.c3p0.max_size", "3");
		adapterDbProps.put("hibernate.connection.autoReconnect", "true");
		repo.setProperties(new Properties());
		repo.getProperties().putAll(adapterDbProps);
		repo.setClassName(repo.getClass().getCanonicalName());
		return repo;
	}

	public static AbstractRepository getMasterDataRepository() {
		LogAnalyzerKPIMaster repo = new LogAnalyzerKPIMaster();
		Map<String, String> adapterDbProps = new HashMap<>();
		adapterDbProps.put("hibernate.connection.url", "***************************************");
		adapterDbProps.put("hibernate.connection.username", "appsone");
		adapterDbProps.put("hibernate.connection.password", "password");
		adapterDbProps.put("hibernate.c3p0.min_size", "1");
		adapterDbProps.put("hibernate.c3p0.max_size", "3");
		adapterDbProps.put("hibernate.connection.autoReconnect", "true");
		repo.setProperties(new Properties());
		repo.getProperties().putAll(adapterDbProps);
		repo.setClassName(repo.getClass().getCanonicalName());
		return repo;
	}

	
	public static AbstractRepository getHealAlertsExternalSystemRepository() {
		HealAlertsExternalSystemRepository repo = new HealAlertsExternalSystemRepository();
		Map<String, String> adapterDbProps = new HashMap<>();
		adapterDbProps.put("hibernate.connection.url", "***************************************");
		adapterDbProps.put("hibernate.connection.username", "appsone");
		adapterDbProps.put("hibernate.connection.password", "password");
		adapterDbProps.put("hibernate.c3p0.min_size", "1");
		adapterDbProps.put("hibernate.c3p0.max_size", "3");
		adapterDbProps.put("hibernate.connection.autoReconnect", "true");
		repo.setProperties(new Properties());
		repo.getProperties().putAll(adapterDbProps);
		repo.setClassName(repo.getClass().getCanonicalName());
		return repo;
	}

}
*/
