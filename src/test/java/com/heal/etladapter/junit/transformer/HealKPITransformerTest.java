package com.heal.etladapter.junit.transformer;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HealKPITransformerTest {}
/*{

	@InjectMocks
	HealKPITransformer testTransformer;

	@Mock
	LogAnalyzerKPIMaster logscanMasterRepository;
	
	AdapterInMemoryCache adapterInMemoryCache = AdapterInMemoryCache.getInstance();

	@Before
	public void init() {
		MockitoAnnotations.initMocks(this);
		Map<String, AbstractRepository> repositoriez = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put(AdapterConstants.ADAPTER_REPOSITORY, "TestRepo");
		params.put(AdapterConstants.APPSONE_REPOSITORY, "AppsoneRepo");
		params.put(AdapterConstants.DOMAIN, "TEST");
		params.put(AdapterConstants.ERROR_PROPERTY,"src/main/resources/error.properties");
		repositoriez.put("TestRepo", logscanMasterRepository);
		//repositoriez.put("AppsoneRepo", appsoneRepo);
		adapterInMemoryCache.setRepositoriez(repositoriez);
		testTransformer.setParameters(params);
	}

	@Test
	public void nonGroupKPITest() {
		LogScanHealInstance instance = new LogScanHealInstance();
		instance.setHealAgentUid("1");
		instance.setHealInstanceName("Test");
		instance.setLogscanInstanceName("Logscan1");
		List<LogScanHealInstance> healInstnaces = new ArrayList<LogScanHealInstance>();
		healInstnaces.add(instance);
		when(logscanMasterRepository.fetchHealInstances()).thenReturn(healInstnaces);
		HealKPI kpi = new HealKPI();
		kpi.setGroup(false);
		kpi.setId(10);
		kpi.setKpiName("Test KPI");
		kpi.setKpiId(100);
		List<HealKPI> healKpiList = new ArrayList<HealKPI>();
		healKpiList.add(kpi);
		when(logscanMasterRepository.getHealKPIs()).thenReturn(healKpiList);

		List<DomainToHealKPIMappings> tempMappings = new ArrayList<DomainToHealKPIMappings>();
		DomainToHealKPIMappings domain = new DomainToHealKPIMappings();
		domain.setDomain("TEST");
		domain.setHealKpiId("100");
		domain.setId(1);
		domain.setSrcKpiId(1000);
		tempMappings.add(domain);
		when(logscanMasterRepository.getDomainToHealKPIMappings()).thenReturn(tempMappings);

		LogScanLogAnalyzerKpi src = new LogScanLogAnalyzerKpi();
		src.setKpiUid(1000);
		src.setValue(10.0);
		src.setUpperThreshold(new Date());
		src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
		src.setLogScanInstanceId("Logscan1");
		AdapterItem item = new AdapterItem();
		item.setSourceItem(src);
		try {
			testTransformer.initialize();
			testTransformer.transform(item);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("error occurred in nonGroupKPITest. Details : ",e);
		}
		Assert.assertEquals(true, item.getDestItem() instanceof KPIAgentMessageProtos.KPIAgentMessage);
		KPIAgentMessageProtos.KPIAgentMessage kpiMessage = (KPIAgentMessage) item.getDestItem();
		Assert.assertEquals(true, kpiMessage.getAgentUid() == "1");
	}

	@Test
	public void groupKPITest() {
		LogScanHealInstance instance = new LogScanHealInstance();
		instance.setHealAgentUid("1");
		instance.setHealInstanceName("Test");
		instance.setLogscanInstanceName("Logscan1");
		List<LogScanHealInstance> healInstnaces = new ArrayList<LogScanHealInstance>();
		healInstnaces.add(instance);
		when(logscanMasterRepository.fetchHealInstances()).thenReturn(healInstnaces);
		HealKPI kpi = new HealKPI();
		kpi.setGroup(true);
		kpi.setGroupName("G1");
		kpi.setId(10);
		kpi.setKpiName("Test KPI");
		kpi.setKpiId(100);
		List<HealKPI> healKpiList = new ArrayList<HealKPI>();
		healKpiList.add(kpi);
		when(logscanMasterRepository.getHealKPIs()).thenReturn(healKpiList);

		List<DomainToHealKPIMappings> tempMappings = new ArrayList<DomainToHealKPIMappings>();
		DomainToHealKPIMappings domain = new DomainToHealKPIMappings();
		domain.setDomain("TEST");
		domain.setHealKpiId("100");
		domain.setId(1);
		domain.setSrcKpiId(1000);

		tempMappings.add(domain);
		when(logscanMasterRepository.getDomainToHealKPIMappings()).thenReturn(tempMappings);

		LogScanLogAnalyzerKpi src = new LogScanLogAnalyzerKpi();
		HashMap<String,String> groupKpis = new HashMap<>();

		groupKpis.put("EXECUTION","1.2");
		groupKpis.put("ELAPSED_S","3.4");
		groupKpis.put("LOCK_PER_EXEC_MS","7.5");

		src.setKpiName("G1");
		src.setKpiUid(1000);
		src.setValue(10.0);
		src.setGroupName("G1");
		src.setIsGroupKpi(true);
		src.setGroupKpis(groupKpis);
		src.setUpperThreshold(new Date());
		src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300));
		src.setLogScanInstanceId("Logscan1");


		AdapterItem item = new AdapterItem();
		item.setSourceItem(src);
		try {
			testTransformer.initialize();
			testTransformer.transform(item);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("error occurred in groupKPITest. Details : ",e);
		}
		Assert.assertEquals(true, item.getDestItem() instanceof KPIAgentMessageProtos.KPIAgentMessage);
		KPIAgentMessageProtos.KPIAgentMessage kpiMessage = (KPIAgentMessage) item.getDestItem();
		Assert.assertEquals(true, kpiMessage.getAgentUid() == "1");
		Assert.assertEquals(true, kpiMessage.getInstances(0).getKpiData(0).getKpiGroupName() == "G1");
	}
}*/

