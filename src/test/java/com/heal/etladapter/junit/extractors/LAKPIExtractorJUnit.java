/*
package com.heal.etladapter.junit.extractors;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig.Builder;
import org.elasticsearch.action.search.MultiSearchRequest;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.jupiter.api.Test;

import com.heal.etladapter.pojos.AdapterItem;
import com.heal.etladapter.cache.AdapterInMemoryCache;
import com.heal.etladapter.repositories.AbstractRepository;
import com.heal.etladapter.chain.AdapterChain;
import com.heal.etladapter.etl.logscan.constants.LogscanConstants;
import com.heal.etladapter.etl.logscan.extractors.LogscanLAKPIExtractor;
import com.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster;
import com.heal.etladapter.utility.DateHelper;
@Slf4j
public class LAKPIExtractorJUnit {

	@Test
	public void basicTest() {
		try {
			LogscanLAKPIExtractor extractor = new LogscanLAKPIExtractor();

			Map<String, String> extractorParams = new HashMap<String, String>();
			extractor.setClassName(LogscanLAKPIExtractor.class.getName());
			extractorParams.put(LogscanConstants.LOGSCAN_ENDPT_ID, "6878");
			extractorParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
			extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_MAXATTEMPTS, "-1");
			extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_RETRYINTERVAL, "60");

			extractor.setParameters(extractorParams);
			extractor.setCurrentChain(new AdapterChain());
			extractor.getCurrentChain().setChainId("Mock Chain");
			LogAnalyzerKPIMaster repo = new LogAnalyzerKPIMaster();
			Map<String, String> adapterDbProps = new HashMap<>();
			adapterDbProps.put("hibernate.connection.url", "***************************************");
			adapterDbProps.put("hibernate.connection.username", "appsone");
			adapterDbProps.put("hibernate.connection.password", "password");
			adapterDbProps.put("hibernate.c3p0.min_size", "1");
			adapterDbProps.put("hibernate.c3p0.max_size", "3");
			adapterDbProps.put("hibernate.connection.autoReconnect", "true");
			repo.setProperties(new Properties());
			repo.getProperties().putAll(adapterDbProps);
			repo.setClassName(repo.getClass().getCanonicalName());
			repo.initialize();

			Map<String, AbstractRepository> repositoriez = new HashMap<String, AbstractRepository>();
			repositoriez.put("LogAnalyzerKPIMaster", repo);
			AdapterInMemoryCache.getInstance().setRepositoriez(repositoriez);

			extractor.initialize();
			List<AdapterItem> itemz = extractor.extract(DateHelper.string2dateConverter("2017-08-08 00:00:00"),
					new Date());
			itemz.forEach(x -> System.out.println(x.getSourceItem()));
		} catch (Exception e) {
			log.error("error occurred in basicTest. Details : ",e);
		}

	}

	//@Test
	public void check4WScrolling() {

		try {
			RestHighLevelClient elasticClient = new RestHighLevelClient(RestClient.builder(
					new HttpHost("127.0.0.1", 9200, "http")).setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback() {
						
						@Override
						public Builder customizeRequestConfig(Builder requestConfigBuilder) {
							return requestConfigBuilder
	                                .setConnectTimeout(900000)
	                                .setSocketTimeout(900000);
						}
					}));
			MultiSearchRequest request = new MultiSearchRequest();
			for (int i = 0; i < 5003; i++) {

				BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
				SearchSourceBuilder searchRequestBuilder = new SearchSourceBuilder();

				MatchPhraseQueryBuilder multiMatchQueryBuilder = QueryBuilders.matchPhraseQuery("payload", "Exception");
				boolQueryBuilder.must(multiMatchQueryBuilder);
				Date from = DateHelper.string2dateConverter("2018-01-01 00:00:00");
				Date to = new Date();
				boolQueryBuilder.must(QueryBuilders.rangeQuery("@timestamp").gte(from).lte(to));
				TermsAggregationBuilder tagg = AggregationBuilders.terms("lakpisearch").field("instance.name");
				searchRequestBuilder.query(boolQueryBuilder);
				searchRequestBuilder.aggregation(tagg);

				SearchRequest searchRequest = new SearchRequest("logscan-raw-*");

				searchRequest.source(searchRequestBuilder);
				searchRequest.searchType(SearchType.QUERY_THEN_FETCH);

				request.add(searchRequest);

			}

			MultiSearchResponse response = elasticClient.msearch(request, RequestOptions.DEFAULT);			
			//System.out.println(response);
			int id = 0;
			for (int index = 0; index < request.requests().size(); index++) {

				if (response.getResponses()[index].getResponse() != null
						&& response.getResponses()[index].getResponse().getAggregations() != null) {

					Map<String, Aggregation> aggz = response.getResponses()[index].getResponse()
							.getAggregations().asMap();
					if (aggz != null) {
						ParsedStringTerms gradeTerms = (ParsedStringTerms) aggz.get("lakpisearch");
						List<? extends Terms.Bucket> listOfBuckets = gradeTerms.getBuckets();

						for (Terms.Bucket bucket : listOfBuckets) {
							System.out.println(id +". " + bucket.getKeyAsString() + " -> " + bucket.getDocCount());
							id++;
						}

					} else {
					}
				} else {
				}
			}

			elasticClient.close();
		} catch (IOException e) {
			log.error("error occurred in check4WScrolling. Details : ",e);
		}

	}
}
*/
