/*
package com.heal.etladapter.junit.extractors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import com.heal.etladapter.pojos.AdapterItem;
import com.heal.etladapter.cache.AdapterInMemoryCache;
import com.heal.etladapter.repositories.AbstractRepository;
import com.heal.etladapter.chain.AdapterChain;
import com.heal.etladapter.etl.logscan.constants.LogscanConstants;
import com.heal.etladapter.etl.logscan.extractors.LogscanLAKPIExtractor;
import com.heal.etladapter.etl.logscan.extractors.LogscanTransactionExtractor;
import com.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster;
import com.heal.etladapter.utility.DateHelper;
@Slf4j
public class LogTransactionExtractorJunit {

	@Test
	public void basicTest() {
		try {
			LogscanTransactionExtractor extractor = new LogscanTransactionExtractor();

			Map<String, String> extractorParams = new HashMap<String, String>();
			extractor.setClassName(LogscanLAKPIExtractor.class.getName());
			extractorParams.put(LogscanConstants.LOGSCAN_ENDPT_ID, "1");
			extractorParams.put(LogscanConstants.LOGSCAN_FIELD_EXISTS_PATTERN, "http.version");
			extractorParams.put(LogscanConstants.LOGSCAN_FIELD_EXISTS_PATTERN_SEPARATOR, ",");
			extractorParams.put(LogscanConstants.LOGSCAN_INDEX_PATTERN, "logstash-*");
			extractorParams.put(LogscanConstants.LOGSCAN_QUERY_FILTER_START, ".gif,.png,.css");
			extractorParams.put(Constants.ADAPTER_REPOSITORY, "LogAnalyzerKPIMaster");
			extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_MAXATTEMPTS, "-1");
			extractorParams.put(LogscanConstants.LOGSCAN_ELASTICSEARCH_RETRYINTERVAL, "60");
			extractorParams.put(LogscanConstants.LOGSCAN_QUERY_SCROLLSIZE,"500");
			extractorParams.put(LogscanConstants.IS_KUBERNETES_TRANSACTIONS,"true");
			extractorParams.put(LogscanConstants.POD_UID,"17");
			extractorParams.put(LogscanConstants.NAME_SPACE,"default,kube-system,monitoring,kube-logging");
			extractorParams.put(LogscanConstants.MATCH_PHASE,"kubernetes.namespace_name");
			extractorParams.put(LogscanConstants.ELASTIC_SEARCH_QUERY,"{\"bool\":{\"must\":[{\"range\":{\"@timestamp\":{\"from\":\"%1$s\",\"to\":\"%2$s\",\"include_lower\":true,\"include_upper\":true,\"format\":\"strict_date_optional_time\",\"boost\":1.0}}},{\"bool\":{\"should\":[{\"match_phrase\":{\"kubernetes.namespace_name\":\"default\"}},{\"match_phrase\":{\"kubernetes.namespace_name\":\"kube-logging\"}},{\"match_phrase\":{\"kubernetes.namespace_name\":\"monitoring\"}},{\"match_phrase\":{\"kubernetes.namespace_name\":\"kube-system\"}}]}}],\"adjust_pure_negative\":true,\"boost\":1.0}}");
			extractorParams.put(LogscanConstants.COMPONENT_SUFFIX,"test_comp_2");

			extractor.setParameters(extractorParams);
			extractor.setCurrentChain(new AdapterChain());
			extractor.getCurrentChain().setChainId("Mock Chain");
			LogAnalyzerKPIMaster repo = new LogAnalyzerKPIMaster();
			Map<String, String> adapterDbProps = new HashMap<>();
			adapterDbProps.put("hibernate.connection.url", "**************************************************");
			adapterDbProps.put("hibernate.connection.username", "appsone");
			adapterDbProps.put("hibernate.connection.password", "password");
			adapterDbProps.put("hibernate.c3p0.min_size", "1");
			adapterDbProps.put("hibernate.c3p0.max_size", "3");
			adapterDbProps.put("hibernate.connection.autoReconnect", "true");
			repo.setProperties(new Properties());
			repo.getProperties().putAll(adapterDbProps);
			repo.setClassName(repo.getClass().getCanonicalName());
			repo.initialize();

			Map<String, AbstractRepository> repositoriez = new HashMap<String, AbstractRepository>();
			repositoriez.put("LogAnalyzerKPIMaster", repo);
			AdapterInMemoryCache.getInstance().setRepositoriez(repositoriez);

			extractor.initialize();
			List<AdapterItem> itemz = extractor.extract(DateHelper.string2dateConverter("2021-11-03 12:00:00"),DateHelper.string2dateConverter("2021-11-03 12:40:00") );//2021-11-03T12:00:36.424Z
			System.out.println("Retrieved {} Transactions" + itemz.size());
			itemz.forEach(x -> System.out.println(x.getSourceItem()));
		} catch (Exception e) {
			log.error("error occurred in basicTest. Details : ",e);
		}

	}
}
*/
