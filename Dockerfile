FROM openjdk:17-jdk-slim

RUN apt update && apt -y install bash openssl

ADD http://192.168.13.69:8081/nexus/repository/third-party/consul-template /usr/local/bin/consul-template
RUN chmod 755 /usr/local/bin/consul-template

ADD ./conf /etc/consul-template/conf
ADD ./templates /etc/consul-template/templates

COPY ./heal-connectors /opt/heal-connectors/
COPY ./entrypoint.sh /opt/heal-connectors/entrypoint.sh

RUN chmod +x /opt/heal-connectors/entrypoint.sh 
 
RUN mkdir -p /tmp/logs

EXPOSE 8989

ENTRYPOINT ["/opt/heal-connectors/entrypoint.sh"]
