# ==========================================================
# RabbitMQ SSL server configuration
# ==========================================================
spring.rabbitmq.addresses={{ key "service/rabbitmq/addresses" }}
spring.rabbitmq.username={{ key "service/rabbitmq/username" }}
spring.rabbitmq.password={{ key "service/rabbitmq/password/encrypted" }}
spring.rabbitmq.ssl.enabled={{ key "service/rabbitmq/sslenable" }}
spring.rabbitmq.ssl.algorithm={{ key "service/rabbitmq/sslprotocol" }}

# ==========================================================
#Mysql Database Configuration
# ==========================================================
spring.datasource.url=jdbc:mysql://{{ key "service/perconadb/node1/ip" }}:{{ key "service/perconadb/node1/port" }}/{{ key "service/perconadb/schema" }}?{{ key "service/perconadb/tls1.2/jdbcparams" }}
spring.datasource.username={{ key "service/perconadb/username" }}
spring.datasource.password={{ key "service/perconadb/password_ui" }}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.minimum.idle.connections={{ key "service/connectors/perconadb/idleconnections" }}
spring.datasource.maximum.pool.size={{ key "service/connectors/perconadb/maxpoolsize" }}
spring.datasource.connection.timeout={{ key "service/connectors/perconadb/connectiontimeout" }}
spring.datasource.connection.idle.timeout={{ key "service/connectors/perconadb/idleconnectiontimeout" }}
spring.datasource.max.life.time={{ key "service/connectors/perconadb/maxlifetime" }}

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes={{ key "service/redis/nodes" }}
spring.redis.ssl={{ key "service/redis/sslenable" }}
spring.redis.username={{key "service/redis/username" }}
spring.redis.password={{key "service/redis/password/encrypted" }}
spring.redis.cluster.mode= {{ key "service/redis/cluster/mode" }}
spring.redis.max.idle.connections={{ key "service/connectors/redis/maxidleconnections" }}
spring.redis.min.idle.connections={{ key "service/connectors/redis/minidleconnections" }}
spring.redis.max.total.connections={{ key "service/connectors/redis/maxtotalconnections" }}
spring.redis.max.wait.sec={{ key "service/connectors/redis/connections/maxwaittimeinsec" }}
spring.redis.share.native.connection={{ key "service/connectors/redis/share/live/connection" }}

# ==========================================================
# Undertow HTTP2 Configuration
# ==========================================================
server.http2.enabled={{ key "service/connectors/http2enabled" }}
server.port={{ key "service/connectors/server/port" }}
server.ssl.enabled-protocols={{ key "service/connectors/sslprotocol" }}
server.ssl.key-store={{ key "service/connectors/keystore" }}
server.ssl.key-store-password={{ key "service/connectors/keystorepassword" }}
server.ssl.trust-store={{ key "service/connectors/truststore" }}
server.ssl.trust-store-password={{ key "service/connectors/truststorepassword" }}

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds={{key "service/connectors/health/metrics/updateinterval/milliseconds" }}
health.metrics.log.interval.milliseconds={{key "service/connectors/health/metrics/loginterval/milliseconds" }}
management.endpoints.web.exposure.include={{key "service/connectors/management/endpoints/web/exposure/include" }}
management.endpoints.jmx.exposure.include={{key "service/connectors/management/endpoints/jmx/exposure/include" }}
management.endpoint.health.enabled={{key "service/connectors/management/endpoints/health/enabled" }}
management.endpoints.web.base-path=/measure
management.server.port={{key "service/connectors/management/server/port" }}
spring.jmx.enabled={{key "service/connectors/jmx/enabled" }}

# ==========================================================
# GRPC Configuration
# ==========================================================
grpc.server.ip={{key "service/grpc/address"}}
grpc.server.port={{key "service/grpc/port"}}
grpc.ssl.enable={{key "service/grpc/enable"}}

# ==========================================================
# Keycloak Configuration
# ==========================================================
keycloak.host={{ key "service/keycloak/hostname" }}
keycloak.port={{ key "service/keycloak/port/https" }}
keycloak.username={{ key "service/keycloak/username" }}
keycloak.password={{ key "service/keycloak/password" }}
