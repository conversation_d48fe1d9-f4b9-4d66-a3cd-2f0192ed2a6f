<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-connectors.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/heal-connectors_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/connectors/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/connectors/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/connectors/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-connectors-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/heal-connectors-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/connectors/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/connectors/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/connectors/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-connectors-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/heal-connectors-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/connectors/auditlogs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/connectors/auditlogs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/connectors/auditlogs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.etladapter" level="{{ key "service/eltadapter/loglevel" }}" additivity="false">
        <appender-ref ref="CORE_FILE"/>
    </logger>

    <logger name="com.heal" level="{{ key "service/connectors/loglevel" }}" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.etladapter.schedulers.StatisticsScheduler" level="{{ key "service/connectors/statsloglevel" }}" additivity="false">
         <appender-ref ref="STATS_FILE"/>
    </logger>

    <logger name="com.heal.etladapter.schedulers.MetricsScheduler" level="{{ key "service/connectors/statsloglevel" }}" additivity="false">
            <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level = "{{ key "service/connectors/rootloglevel" }}">
        <appender-ref ref = "CORE_FILE"/>
    </root>
</configuration>