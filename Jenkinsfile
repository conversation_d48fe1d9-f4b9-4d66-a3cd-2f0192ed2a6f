pipeline {
    agent { label 'Second_Slave' }

    tools {
        jdk 'openjdk-17-second'
        maven 'Maven_388'
    }

    environment {
        NEXUS_COMMON_CREDS = credentials('0981d455-e100-4f93-9faf-151ac7e29d8a')
        NEXUS_URL = 'http://*************:8081'
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '5'))
        office365ConnectorWebhooks([[
            name: '<PERSON>',
            notifyBackToNormal: true,
            notifyFailure: true,
            notifySuccess: true,
            notifyUnstable: true,
            url: "https://healsoftwareai.webhook.office.com/webhookb2/78345e71-2972-44c4-a270-fbae82662bf1@55dca2af-e23a-4402-b9a6-8833b28a02dc/JenkinsCI/7958868126734afeb78edb01dafdcc05/6fed72e3-b7dd-422f-9075-e6d96468feb0"
        ]])
    }

    parameters {
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'etl_adapter_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*etl-adapter.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'motadata_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*motadata-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'dynatrace_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*dynatrace-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'appdynamics_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*appdynamics-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'prometheus_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*prometheus-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'datapower_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*datapower-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'kubernetes_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*kubernetes-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'elasticsearch_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*elasticsearch-connector.git')
        gitParameter(branch: '', branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'riverbed_connector_branch', quickFilterEnabled: true, sortMode: 'NONE', selectedValue: 'NONE', type: 'PT_BRANCH_TAG', useRepository: '.*riverbed-connector.git')

    }

    stages {
        stage('Checkout') {
            parallel {
                stage('etl-adapter') {
                    steps {
                        dir('etl-adapter') {
                            git branch: "${params.etl_adapter_branch}", url: 'https://<EMAIL>/appsone/etl-adapter.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('motadata-connector') {
                    steps {
                        dir('motadata-connector') {
                            git branch: "${params.motadata_connector_branch}", url: 'https://<EMAIL>/appsone/motadata-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('dynatrace-connector') {
                    steps {
                        dir('dynatrace-connector') {
                            git branch: "${params.dynatrace_connector_branch}", url: 'https://<EMAIL>/appsone/dynatrace-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('appdynamics-connector') {
                    steps {
                        dir('appdynamics-connector') {
                            git branch: "${params.appdynamics_connector_branch}", url: 'https://<EMAIL>/appsone/appdynamics-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('prometheus-connector') {
                    steps {
                        dir('prometheus-connector') {
                            git branch: "${params.prometheus_connector_branch}", url: 'https://<EMAIL>/appsone/prometheus-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('datapower-connector') {
                    steps {
                        dir('datapower-connector') {
                            git branch: "${params.datapower_connector_branch}", url: 'https://<EMAIL>/appsone/datapower-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('kubernetes-connector') {
                    steps {
                        dir('kubernetes-connector') {
                            git branch: "${params.kubernetes_connector_branch}", url: 'https://<EMAIL>/appsone/kubernetes-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('elasticsearch-connector') {
                    steps {
                        dir('elasticsearch-connector') {
                            git branch: "${params.elasticsearch_connector_branch}", url: 'https://<EMAIL>/appsone/elasticsearch-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
                stage('riverbed-connector') {
                    steps {
                        dir('riverbed-connector') {
                            git branch: "${params.riverbed_connector_branch}", url: 'https://<EMAIL>/appsone/riverbed-connector.git', credentialsId: 'fd197b00-fd06-4632-a018-36134111e086'
                        }
                    }
                }
            }
        }

        stage('Build') {
            steps {
                script {
                    def builds = [:]
                    def repos = ['etl-adapter', 'motadata-connector', 'dynatrace-connector', 'appdynamics-connector',
                                 'prometheus-connector', 'datapower-connector', 'kubernetes-connector', 'elasticsearch-connector', 'riverbed-connector']

                    repos.each { repo ->
                        def branch = params["${repo.replace('-', '_')}_branch"]
                        if (branch?.trim() && branch != 'master')  {
                            builds["Build ${repo}"] = {
                                dir(repo) {
                                    if (fileExists('pom.xml')) {
                                        withSonarQubeEnv('sonarqube_71') {
                                            sh 'mvn clean deploy -U sonar:sonar'
                                        }
                                    } else {
                                        echo "Skipping build for ${repo}: pom.xml not found"
                                    }
                                }
                            }
                        }
                    }
                    parallel builds
                }
            }
        }

        
        stage('Extract tar and copy') {
            steps {
                script {
                    def tarDir = "${env.WORKSPACE}/heal-connectors"
                    sh "mkdir -p ${tarDir}"

                    ['etl-adapter'].each { repo ->
                        dir(repo) {
                            def tarball = sh(script: "ls target/heal-etl-adapter-*.tar.gz 2>/dev/null || true", returnStdout: true).trim()
                            if (tarball) {
                                def tarName = tarball.split('/')[-1]
                                sh """
                                    cd target
                                    tar -xvf ${tarName}
                                    cd heal-etl-adapter
                                    cp -r config heal-connector.sh heal-etl-adapter-*.jar lib logs version.txt "${tarDir}"
                                """
                            } else {
                                echo "No tar.gz file found in ${repo}/target, skipping extraction."
                            }
                        }
                    }
                }
            }
        }
 
        


        stage('Copy JARs') {
            steps {
                script {
                    def jarDir = "${env.WORKSPACE}/heal-connectors/lib"
                    sh "mkdir -p ${jarDir}"

                    ['motadata-connector', 'dynatrace-connector', 'appdynamics-connector',
                     'prometheus-connector', 'datapower-connector', 'kubernetes-connector', 'elasticsearch-connector', 'riverbed-connector'].each { repo ->
                        dir(repo) {
                            if (fileExists('target')) {
                                sh """cp target/*.jar "${jarDir}" || echo 'No JARs to copy from ${repo}'"""
                            } else {
                                echo "No target/ directory found in ${repo}, skipping JAR copy."
                            }
                        }
                    }
                    if (fileExists('lib')) {
                        archiveArtifacts artifacts: 'lib/*.jar', allowEmptyArchive: true
                    } else {
                        echo "No lib directory found to archive artifacts"
                    }
                }
            }
        }

        stage('Copy lib/*jar files') {
            steps {
                script {
                    def libDir = "${env.WORKSPACE}/heal-connectors/lib"
                    sh "mkdir -p ${libDir}"

                    def repos = [
                        'motadata-connector', 'dynatrace-connector', 'appdynamics-connector',
                        'prometheus-connector', 'datapower-connector', 'kubernetes-connector',
                        'elasticsearch-connector', 'riverbed-connector'
                    ]

                    repos.each { repo ->
                        dir(repo) {
                            def libtarball = sh(script: "ls target/*.tar.gz 2>/dev/null || true", returnStdout: true).trim()
                            if (libtarball) {
                                def tarName = libtarball.tokenize('/').last()
                                sh """
                                    cd target
                                    tar -xvf ${tarName}
                                    extracted_dir=\$(tar -tzf ${tarName} | head -1 | cut -f1 -d"/")
                                    cp -f \$extracted_dir/lib/*.jar "${libDir}"
                                """
                            } else {
                                    echo "No tar.gz file found in ${repo}/target, skipping extraction."
                            }        
                        }
                    }
                }
            }
        }
      
        stage('Docker build') {
            steps {
                script {
                    pom = readMavenPom file: 'pom.xml'
                    version = pom.version
                    echo "Building Docker image with version: ${version}"
                    sh "cp -r \"${env.WORKSPACE}/heal-connectors\" ."
                    sh "docker build -t heal-connector:${version} ."
                    env.PROJECT_VERSION = version
                }
            }
        }

        stage('Publish Docker Image') {
            steps {
                script {
                    def version = env.PROJECT_VERSION
                    if (!version) {
                        error "PROJECT_VERSION not set, skipping Docker image publish."
                    }
                    sh "docker save heal-connector:${version} > heal-connector_${version}.tar"
                    sh "curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-connector_${version}.tar ${NEXUS_URL}/nexus/repository/tls_docker_images/heal-connector_${version}.tar"
                    writeFile file: '/tmp/heal-connector_version', text: "heal-connector_${version}"
                }
            }
        }

        stage('Docker VAPT scan') {
            when {
                expression { params.etl_adapter_branch == 'develop' }
            }
            steps {
                script {
                    def version = env.PROJECT_VERSION
                    sh "docker scan heal-connector:${version} > heal-connector.txt || true"
                    sh "curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-connector.txt ${NEXUS_URL}/nexus/repository/Image_scan_report/heal-connector.txt"
                }
            }
            post {
                success {
                    emailext(
                        attachmentsPattern: 'heal-connector.txt',
                        subject: 'Docker VAPT Scan Report',
                        body: '''Hi Team,<br><br> Please find the attached VAPT report for your reference.<br><br>Kindly review.<br><br>Thanks,<br>Jenkins''',
                        mimeType: 'text/html',
                        to: '<EMAIL>'
                    )
                }
            }
                }

        stage('Cleanup') {
            steps {
                script {
                    def version = env.PROJECT_VERSION
                    sh "docker rmi -f heal-connector:${version} || true"
                    cleanWs()
                }
            }
        }
    }
}
